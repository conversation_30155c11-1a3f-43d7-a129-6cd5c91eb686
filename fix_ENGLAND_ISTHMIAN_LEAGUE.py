#!/usr/bin/env python3
"""
England Isthmian League Team Mapping Fix

Auto-generated fix script for leagues under 90% success rate.
"""

import sys
import importlib.util
from typing import Dict, List, Tuple

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

LEAGUE_NAME = "ENGLAND_ISTHMIAN_LEAGUE"
LEAGUE_DISPLAY_NAME = "England Isthmian League"

def load_league_config() -> Dict[str, str]:
    """Load the league config mappings if available."""
    config_path = f"src/scrapers/league_configs/{LEAGUE_NAME}.py"
    
    try:
        spec = importlib.util.spec_from_file_location("config", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        
        current_config = getattr(config_module, 'CURRENT_CONFIG', None)
        if current_config and 'TEAM_NAME_MAPPING' in current_config:
            return current_config['TEAM_NAME_MAPPING']
        
        team_mapping = getattr(config_module, 'TEAM_NAME_MAPPING', None)
        if team_mapping:
            return team_mapping
            
        return {}
    except Exception as e:
        print(f"⚠️  No config file found or error loading: {e}")
        return {}

def analyze_current_state():
    """Analyze the current state of the league."""
    print(f"🔍 ANALYZING {LEAGUE_DISPLAY_NAME} CURRENT STATE")
    print("=" * 60)
    
    with get_database() as db:
        teams = db.execute_query('''
            SELECT t.team_name, t.team_id, t.canonical_team_id, 
                   t2.team_name as canonical_name
            FROM teams t
            LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY t.team_name
        ''', (LEAGUE_NAME,))
        
        if teams.empty:
            print(f"❌ No teams found for {LEAGUE_NAME}")
            return {}
        
        print(f"📊 Found {len(teams)} teams in {LEAGUE_DISPLAY_NAME}")
        
        teams_info = {}
        working_teams = 0
        
        for _, team in teams.iterrows():
            team_name = team['team_name']
            team_id = team['team_id']
            canonical_name = team['canonical_name']
            
            stats = db.get_team_stats(team_name, LEAGUE_NAME)
            has_stats = not stats.empty
            
            if has_stats:
                working_teams += 1
            
            teams_info[team_name] = {
                'team_id': team_id,
                'canonical_name': canonical_name,
                'has_stats': has_stats,
                'canonical_id': team['canonical_team_id']
            }
            
            status = "✅" if has_stats else "❌"
            canonical_info = f" → {canonical_name}" if canonical_name != team_name else ""
            print(f"   {status} {team_name}{canonical_info}")
        
        success_rate = (working_teams / len(teams)) * 100
        print(f"\n📊 Current success rate: {working_teams}/{len(teams)} ({success_rate:.1f}%)")
        
        return teams_info

def apply_config_mappings(config_mappings: Dict[str, str]):
    """Apply config mappings if available."""
    if not config_mappings:
        print("⚠️  No config mappings to apply")
        return 0
    
    print(f"\n🔧 APPLYING CONFIG MAPPINGS")
    print("=" * 50)
    
    fixes_applied = 0
    
    with get_database() as db:
        for current_team, old_team in config_mappings.items():
            print(f"\n📋 Processing: {current_team} ← {old_team}")
            
            current_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (current_team, LEAGUE_NAME))
            
            old_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (old_team, LEAGUE_NAME))
            
            if not current_id or not old_id:
                print(f"   ⚠️  Team not found")
                continue
            
            current_stats = db.get_team_stats(current_team, LEAGUE_NAME)
            old_stats = db.get_team_stats(old_team, LEAGUE_NAME)
            
            if current_stats.empty and old_stats.empty:
                continue
            
            if not current_stats.empty and old_stats.empty:
                target_id, source_id = current_id, old_id
                direction = f"{old_team} → {current_team}"
            elif current_stats.empty and not old_stats.empty:
                target_id, source_id = old_id, current_id
                direction = f"{current_team} → {old_team}"
            else:
                continue
            
            try:
                db.conn.execute(
                    'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                    (target_id, source_id)
                )
                print(f"   ✅ Applied mapping: {direction}")
                fixes_applied += 1
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        if fixes_applied > 0:
            db.conn.commit()
            print(f"\n💾 Committed {fixes_applied} config-based fixes")
    
    return fixes_applied

def fix_circular_references():
    """Fix teams with stats that point to teams without stats."""
    print(f"\n🚨 CHECKING FOR CIRCULAR REFERENCES")
    print("=" * 50)

    fixes_applied = 0

    with get_database() as db:
        problematic_teams = db.execute_query('''
            SELECT t.team_id, t.team_name, t.canonical_team_id,
                   COUNT(ts.team_id) as stat_count
            FROM teams t
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ? AND t.team_id != t.canonical_team_id
            GROUP BY t.team_id, t.team_name, t.canonical_team_id
            HAVING stat_count > 0
        ''', (LEAGUE_NAME,))

        if problematic_teams.empty:
            print("✅ No circular references found")
            return 0

        print(f"Found {len(problematic_teams)} teams with stats pointing to other teams:")

        for _, team in problematic_teams.iterrows():
            team_id = team['team_id']
            team_name = team['team_name']
            stat_count = team['stat_count']

            print(f"\n🔧 Fixing: {team_name} (ID: {team_id})")
            print(f"   Has {stat_count} stats but points to other team")
            print(f"   Making it canonical (point to itself)")

            db.conn.execute(
                'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                (team_id, team_id)
            )
            fixes_applied += 1

        if fixes_applied > 0:
            db.conn.commit()
            print(f"\n💾 Committed {fixes_applied} circular reference fixes")

    return fixes_applied

def verify_final_state():
    """Verify the final state after all fixes."""
    print(f"\n📊 FINAL {LEAGUE_DISPLAY_NAME} STATUS")
    print("=" * 50)
    
    with get_database() as db:
        teams = db.execute_query('''
            SELECT t.team_name, t2.team_name as canonical_name
            FROM teams t
            LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY t.team_name
        ''', (LEAGUE_NAME,))
        
        working_teams = 0
        total_teams = len(teams)
        
        for _, team in teams.iterrows():
            team_name = team['team_name']
            canonical_name = team['canonical_name']
            
            stats = db.get_team_stats(team_name, LEAGUE_NAME)
            has_stats = not stats.empty
            
            if has_stats:
                working_teams += 1
            
            status = "✅" if has_stats else "❌"
            canonical_info = f" → {canonical_name}" if canonical_name != team_name else ""
            print(f"   {status} {team_name}{canonical_info}")
        
        success_rate = (working_teams / total_teams) * 100
        print(f"\n📈 Final Results:")
        print(f"   Working teams: {working_teams}/{total_teams}")
        print(f"   Success rate: {success_rate:.1f}%")
        
        return working_teams, total_teams

def main():
    """Main function."""
    print(f"🏆 {LEAGUE_DISPLAY_NAME.upper()} TEAM MAPPING FIX")
    print("=" * 70)
    print(f"Fixing canonical team mappings for {LEAGUE_DISPLAY_NAME}")
    
    teams_info = analyze_current_state()
    if not teams_info:
        return
    
    config_mappings = load_league_config()
    config_fixes = apply_config_mappings(config_mappings)
    
    circular_fixes = fix_circular_references()

    working, total = verify_final_state()
    
    total_fixes = config_fixes + circular_fixes
    print(f"\n🎉 {LEAGUE_DISPLAY_NAME.upper()} FIX COMPLETE!")
    print(f"Applied {total_fixes} canonical mapping fixes")
    if circular_fixes > 0:
        print(f"🚨 Fixed {circular_fixes} critical circular references!")
    print(f"Final success rate: {(working/total)*100:.1f}% ({working}/{total} teams)")
    
    if total_fixes > 0:
        print(f"🎯 Applied {total_fixes} fixes!")
    
    if working == total:
        print(f"🎯 Perfect! All {LEAGUE_DISPLAY_NAME} teams now have stats!")
    elif (working/total)*100 >= 90:
        print(f"🎯 Success! Achieved 90%+ target success rate!")
    else:
        remaining = total - working
        print(f"⚠️  {remaining} teams still need attention")

if __name__ == "__main__":
    main()
