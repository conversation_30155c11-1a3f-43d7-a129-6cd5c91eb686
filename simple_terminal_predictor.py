#!/usr/bin/env python3
"""
Simple Terminal Prediction Interface

A lightweight command-line interface for making football match predictions
using the database and basic statistical models.
"""

import sys
import os
import argparse
from typing import Dict, List, Tuple, Optional
from datetime import datetime

# Add src to path
sys.path.append('src')

try:
    from database.football_db import get_database
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure you're running from the project root directory.")
    sys.exit(1)

class SimpleTerminalPredictor:
    """Simple terminal prediction interface using database and basic stats."""
    
    def __init__(self):
        self.current_league = None
        self.db = None
        
    def initialize(self):
        """Initialize the prediction system."""
        print("⚽ Simple Terminal Football Predictor")
        print("=" * 40)
        
        try:
            self.db = get_database()
            print("✅ Database connected successfully")
            return True
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False
            
    def get_available_leagues(self) -> List[str]:
        """Get list of available leagues from database."""
        try:
            with self.db as db:
                leagues = db.execute_query("SELECT DISTINCT league_name FROM leagues ORDER BY league_name")
                return leagues['league_name'].tolist() if not leagues.empty else []
        except Exception as e:
            print(f"❌ Error getting leagues: {e}")
            return []
            
    def display_leagues(self):
        """Display available leagues."""
        leagues = self.get_available_leagues()
        if not leagues:
            print("❌ No leagues found in database")
            return
            
        print(f"\n📊 Available Leagues ({len(leagues)} total):")
        print("-" * 40)
        
        # Group by country
        league_groups = {}
        for league in leagues:
            country = league.split('_')[0]
            if country not in league_groups:
                league_groups[country] = []
            league_groups[country].append(league)
            
        for country, country_leagues in sorted(league_groups.items()):
            print(f"\n🏴 {country.title()}:")
            for league in sorted(country_leagues)[:5]:  # Show first 5
                display_name = league.replace('_', ' ').title()
                print(f"  • {display_name}")
            if len(country_leagues) > 5:
                print(f"  ... and {len(country_leagues) - 5} more")
                
    def select_league(self, league_name: Optional[str] = None) -> bool:
        """Select a league for predictions."""
        available_leagues = self.get_available_leagues()
        
        if league_name:
            if league_name in available_leagues:
                self.current_league = league_name
                print(f"✅ Selected: {league_name.replace('_', ' ').title()}")
                return True
            else:
                print(f"❌ League '{league_name}' not found")
                return False
                
        self.display_leagues()
        print(f"\n🎯 Enter league name:")
        user_input = input("> ").strip().upper().replace(' ', '_')
        
        # Try exact match first
        if user_input in available_leagues:
            self.current_league = user_input
            print(f"✅ Selected: {user_input.replace('_', ' ').title()}")
            return True
            
        # Try partial match
        matches = [league for league in available_leagues if user_input in league]
        if len(matches) == 1:
            self.current_league = matches[0]
            print(f"✅ Selected: {matches[0].replace('_', ' ').title()}")
            return True
        elif len(matches) > 1:
            print(f"🤔 Multiple matches found:")
            for i, match in enumerate(matches[:10], 1):
                print(f"  {i}. {match.replace('_', ' ').title()}")
            try:
                choice = int(input("Select number: ")) - 1
                if 0 <= choice < len(matches):
                    self.current_league = matches[choice]
                    print(f"✅ Selected: {matches[choice].replace('_', ' ').title()}")
                    return True
            except (ValueError, IndexError):
                pass
                
        print("❌ League not found")
        return False
        
    def get_teams_in_league(self) -> List[str]:
        """Get teams in current league."""
        if not self.current_league:
            return []
            
        try:
            with self.db as db:
                teams = db.execute_query('''
                    SELECT DISTINCT t.team_name 
                    FROM teams t
                    JOIN leagues l ON t.league_id = l.league_id
                    WHERE l.league_name = ?
                    ORDER BY t.team_name
                ''', (self.current_league,))
                return teams['team_name'].tolist() if not teams.empty else []
        except Exception as e:
            print(f"❌ Error getting teams: {e}")
            return []
            
    def get_team_stats(self, team_name: str) -> Dict:
        """Get basic stats for a team."""
        if not self.current_league:
            return {}
            
        try:
            with self.db as db:
                stats = db.get_team_stats(team_name, self.current_league)
                if not stats.empty:
                    return stats.iloc[0].to_dict()
                return {}
        except Exception as e:
            print(f"❌ Error getting team stats: {e}")
            return {}
            
    def predict_match_simple(self, home_team: str, away_team: str) -> Dict:
        """Make a simple prediction based on team stats."""
        print(f"\n🔮 Predicting: {home_team} vs {away_team}")
        print("-" * 50)
        
        # Get team stats
        home_stats = self.get_team_stats(home_team)
        away_stats = self.get_team_stats(away_team)
        
        if not home_stats or not away_stats:
            return {"error": "Team statistics not available"}
            
        # Calculate basic metrics
        try:
            home_ppg = float(home_stats.get('points_per_game', 0))
            away_ppg = float(away_stats.get('points_per_game', 0))
            
            home_goals_for = float(home_stats.get('goals_for_per_game', 0))
            home_goals_against = float(home_stats.get('goals_against_per_game', 0))
            away_goals_for = float(away_stats.get('goals_for_per_game', 0))
            away_goals_against = float(away_stats.get('goals_against_per_game', 0))
            
            # Simple prediction logic
            home_strength = home_ppg + home_goals_for - home_goals_against
            away_strength = away_ppg + away_goals_for - away_goals_against
            
            # Add home advantage
            home_strength += 0.3
            
            # Calculate probabilities (simplified)
            total_strength = home_strength + away_strength + 1.0  # Add 1 for draw
            
            if total_strength > 0:
                home_prob = max(0.1, min(0.8, home_strength / total_strength))
                away_prob = max(0.1, min(0.8, away_strength / total_strength))
                draw_prob = max(0.1, 1.0 - home_prob - away_prob)
                
                # Normalize
                total_prob = home_prob + draw_prob + away_prob
                home_prob /= total_prob
                draw_prob /= total_prob
                away_prob /= total_prob
            else:
                home_prob = draw_prob = away_prob = 0.33
                
            # Determine prediction
            if home_prob > draw_prob and home_prob > away_prob:
                prediction = "Home Win"
            elif away_prob > draw_prob and away_prob > home_prob:
                prediction = "Away Win"
            else:
                prediction = "Draw"
                
            # Expected goals (simplified)
            home_xg = (home_goals_for + (2.0 - away_goals_against)) / 2
            away_xg = (away_goals_for + (2.0 - home_goals_against)) / 2
            
            # Most likely score
            home_score = max(0, round(home_xg))
            away_score = max(0, round(away_xg))
            
            return {
                "prediction": prediction,
                "probabilities": {
                    "Home Win": home_prob,
                    "Draw": draw_prob,
                    "Away Win": away_prob
                },
                "expected_goals": {
                    "home": home_xg,
                    "away": away_xg
                },
                "most_likely_score": f"{home_score}-{away_score}",
                "confidence": max(home_prob, draw_prob, away_prob),
                "team_stats": {
                    "home": {
                        "ppg": home_ppg,
                        "goals_for": home_goals_for,
                        "goals_against": home_goals_against
                    },
                    "away": {
                        "ppg": away_ppg,
                        "goals_for": away_goals_for,
                        "goals_against": away_goals_against
                    }
                }
            }
            
        except Exception as e:
            return {"error": f"Prediction calculation failed: {e}"}
            
    def display_prediction(self, home_team: str, away_team: str, prediction: Dict):
        """Display prediction results."""
        if "error" in prediction:
            print(f"❌ {prediction['error']}")
            return
            
        print(f"\n🎯 PREDICTION RESULTS")
        print("=" * 50)
        print(f"🏠 Home: {home_team}")
        print(f"✈️  Away: {away_team}")
        print(f"🕒 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print(f"\n📊 PREDICTION: {prediction['prediction']}")
        print(f"🎯 Confidence: {prediction['confidence']:.1%}")
        
        print(f"\n📈 PROBABILITIES:")
        probs = prediction['probabilities']
        print(f"   🏠 Home Win: {probs['Home Win']:.1%}")
        print(f"   🤝 Draw:     {probs['Draw']:.1%}")
        print(f"   ✈️  Away Win: {probs['Away Win']:.1%}")
        
        print(f"\n⚽ EXPECTED GOALS:")
        xg = prediction['expected_goals']
        print(f"   🏠 {home_team}: {xg['home']:.1f}")
        print(f"   ✈️  {away_team}: {xg['away']:.1f}")
        
        print(f"\n🎲 MOST LIKELY SCORE: {prediction['most_likely_score']}")
        
        # Team comparison
        home_stats = prediction['team_stats']['home']
        away_stats = prediction['team_stats']['away']
        
        print(f"\n📊 TEAM COMPARISON:")
        print(f"   Points/Game:  {home_stats['ppg']:.1f} vs {away_stats['ppg']:.1f}")
        print(f"   Goals For:    {home_stats['goals_for']:.1f} vs {away_stats['goals_for']:.1f}")
        print(f"   Goals Against: {home_stats['goals_against']:.1f} vs {away_stats['goals_against']:.1f}")
        
    def interactive_prediction(self):
        """Interactive prediction interface."""
        if not self.current_league:
            print("❌ No league selected. Please select a league first.")
            return
            
        teams = self.get_teams_in_league()
        if not teams:
            print("❌ No teams found in selected league")
            return
            
        print(f"\n⚽ Match Prediction - {self.current_league.replace('_', ' ').title()}")
        print(f"📊 {len(teams)} teams available")
        
        # Show some teams
        print(f"\nSample teams:")
        for team in teams[:10]:
            print(f"  • {team}")
        if len(teams) > 10:
            print(f"  ... and {len(teams) - 10} more")
            
        # Get home team
        print(f"\n🏠 Enter home team name:")
        home_input = input("> ").strip()
        home_team = self._match_team(home_input, teams)
        
        if not home_team:
            print("❌ Home team not found")
            return
            
        # Get away team
        print(f"\n✈️  Enter away team name:")
        away_input = input("> ").strip()
        away_team = self._match_team(away_input, teams)
        
        if not away_team:
            print("❌ Away team not found")
            return
            
        if home_team == away_team:
            print("❌ Cannot select the same team twice")
            return
            
        # Make prediction
        prediction = self.predict_match_simple(home_team, away_team)
        self.display_prediction(home_team, away_team, prediction)
        
    def _match_team(self, user_input: str, teams: List[str]) -> Optional[str]:
        """Match user input to team name."""
        # Exact match
        for team in teams:
            if user_input.lower() == team.lower():
                return team
                
        # Partial match
        matches = [team for team in teams if user_input.lower() in team.lower()]
        if len(matches) == 1:
            return matches[0]
        elif len(matches) > 1:
            print(f"🤔 Multiple matches found:")
            for i, match in enumerate(matches[:5], 1):
                print(f"  {i}. {match}")
            try:
                choice = int(input("Select number: ")) - 1
                if 0 <= choice < len(matches):
                    return matches[choice]
            except (ValueError, IndexError):
                pass
                
        return None

    def show_league_info(self):
        """Show information about current league."""
        if not self.current_league:
            print("❌ No league selected")
            return

        try:
            with self.db as db:
                # Get league info
                league_info = db.execute_query('''
                    SELECT COUNT(DISTINCT t.team_id) as team_count
                    FROM teams t
                    JOIN leagues l ON t.league_id = l.league_id
                    WHERE l.league_name = ?
                ''', (self.current_league,))

                # Get teams with stats
                teams_with_stats = db.execute_query('''
                    SELECT COUNT(DISTINCT t.team_id) as working_teams
                    FROM teams t
                    JOIN leagues l ON t.league_id = l.league_id
                    JOIN team_stats ts ON t.team_id = ts.team_id
                    WHERE l.league_name = ?
                ''', (self.current_league,))

                team_count = league_info.iloc[0]['team_count'] if not league_info.empty else 0
                working_teams = teams_with_stats.iloc[0]['working_teams'] if not teams_with_stats.empty else 0

                print(f"\n📊 League Information: {self.current_league.replace('_', ' ').title()}")
                print("=" * 50)
                print(f"🏟️  Total Teams: {team_count}")
                print(f"📈 Teams with Stats: {working_teams}")
                if team_count > 0:
                    success_rate = (working_teams / team_count) * 100
                    print(f"✅ Success Rate: {success_rate:.1f}%")

        except Exception as e:
            print(f"❌ Error getting league info: {e}")

    def interactive_menu(self):
        """Main interactive menu."""
        while True:
            print(f"\n🎮 SIMPLE TERMINAL PREDICTOR")
            print("=" * 35)

            if self.current_league:
                print(f"📊 Current League: {self.current_league.replace('_', ' ').title()}")
            else:
                print("📊 No league selected")

            print("\nOptions:")
            print("  1. Select League")
            print("  2. Predict Match")
            print("  3. Show League Info")
            print("  4. List Teams")
            print("  5. Exit")

            try:
                choice = input("\nSelect option (1-5): ").strip()

                if choice == '1':
                    self.select_league()
                elif choice == '2':
                    self.interactive_prediction()
                elif choice == '3':
                    self.show_league_info()
                elif choice == '4':
                    self._show_teams()
                elif choice == '5':
                    print("👋 Goodbye!")
                    break
                else:
                    print("❌ Invalid option. Please select 1-5.")

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

    def _show_teams(self):
        """Show all teams in current league."""
        if not self.current_league:
            print("❌ No league selected")
            return

        teams = self.get_teams_in_league()
        if not teams:
            print("❌ No teams found")
            return

        print(f"\n🏟️  Teams in {self.current_league.replace('_', ' ').title()}:")
        print("=" * 50)

        for i, team in enumerate(teams, 1):
            print(f"  {i:2d}. {team}")

        print(f"\nTotal: {len(teams)} teams")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Simple Terminal Football Predictor",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python simple_terminal_predictor.py                    # Interactive mode
  python simple_terminal_predictor.py --league ENGLAND_PREMIER_LEAGUE  # Load specific league
  python simple_terminal_predictor.py --predict "Arsenal vs Chelsea"   # Quick prediction
        """
    )

    parser.add_argument(
        '--league',
        type=str,
        help='League to load automatically'
    )
    parser.add_argument(
        '--predict',
        type=str,
        help='Quick prediction in format "Home Team vs Away Team"'
    )

    args = parser.parse_args()

    # Initialize predictor
    predictor = SimpleTerminalPredictor()
    if not predictor.initialize():
        return 1

    # Handle command line arguments
    if args.league:
        if not predictor.select_league(args.league):
            return 1

    if args.predict:
        if not predictor.current_league:
            print("❌ No league loaded. Use --league to specify a league.")
            return 1

        # Parse prediction
        if ' vs ' in args.predict:
            home_team, away_team = args.predict.split(' vs ', 1)
            teams = predictor.get_teams_in_league()

            home_match = predictor._match_team(home_team.strip(), teams)
            away_match = predictor._match_team(away_team.strip(), teams)

            if home_match and away_match:
                prediction = predictor.predict_match_simple(home_match, away_match)
                predictor.display_prediction(home_match, away_match, prediction)
                return 0
            else:
                print("❌ One or both teams not found")
                return 1
        else:
            print("❌ Invalid format. Use: 'Home Team vs Away Team'")
            return 1

    # Interactive mode
    predictor.interactive_menu()
    return 0


if __name__ == "__main__":
    sys.exit(main())
