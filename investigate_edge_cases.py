#!/usr/bin/env python3
"""
Investigate Edge Case Leagues

This script focuses on the 40 leagues that applied fixes but showed no improvement.
These are the most interesting cases that need manual investigation.
"""

import sys
import importlib.util
import os
from typing import Dict, List, Tuple, Set
from collections import defaultdict

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

# ============================================================================
# EDGE CASE LEAGUES (from comprehensive batch processing)
# ============================================================================

# These leagues applied fixes but showed no improvement - need investigation
EDGE_CASE_LEAGUES = [
    "ARMENIA_PREMIER_LEAGUE",  # 100% success rate - investigate why fixes were applied
    "AUSTRALIA_A_LEAGUE",     # High success rate but applied fixes
    "AUSTRIA_BUNDESLIGA",     # Applied fixes but no improvement
    "BELGIUM_FIRST_DIVISION_A", # Applied fixes but no improvement
    "BRAZIL_SERIE_A",         # Major league - should investigate
    "BULGARIA_FIRST_LEAGUE",  # Applied fixes but no improvement
    "CANADA_PREMIER_LEAGUE",  # Applied fixes but no improvement
    "CHILE_PRIMERA_DIVISION", # Applied fixes but no improvement
    "COLOMBIA_PRIMERA_A",     # Applied fixes but no improvement
    "COSTA_RICA_PRIMERA_DIVISION", # Applied fixes but no improvement
    "CROATIA_PRVA_HNL",       # Applied fixes but no improvement
    "CZECH_REPUBLIC_FIRST_LEAGUE", # Applied fixes but no improvement
    "DENMARK_SUPERLIGA",     # Applied fixes but no improvement
    "ECUADOR_SERIE_A",       # Applied fixes but no improvement
    "ENGLAND_CHAMPIONSHIP",  # Major league - should investigate
    "ENGLAND_LEAGUE_1",      # Applied fixes but no improvement
    "ENGLAND_PREMIER_LEAGUE", # Major league - should investigate
    "ESTONIA_MEISTRILIIGA",  # Applied fixes but no improvement
    "FINLAND_VEIKKAUSLIIGA", # Applied fixes but no improvement
    "FRANCE_LIGUE_1",        # Major league - should investigate
    "FRANCE_LIGUE_2",        # Applied fixes but no improvement
    "GEORGIA_EROVNULI_LIGA", # Applied fixes but no improvement
    "GERMANY_BUNDESLIGA",    # Major league - should investigate
    "GREECE_SUPER_LEAGUE",   # Applied fixes but no improvement
    "HUNGARY_NB_I",          # Applied fixes but no improvement
    "ICELAND_URVALSDEILD",   # Applied fixes but no improvement
    "IRELAND_PREMIER_DIVISION", # Applied fixes but no improvement
    "ISRAEL_PREMIER_LEAGUE", # Applied fixes but no improvement
    "ITALY_SERIE_A",         # Major league - should investigate
    "JAPAN_J1_LEAGUE",       # Applied fixes but no improvement
    "LATVIA_VIRSLIGA",       # Applied fixes but no improvement
    "LUXEMBOURG_NATIONAL_DIVISION", # Applied fixes but no improvement
    "MALTA_PREMIER_LEAGUE",  # Applied fixes but no improvement
    "MEXICO_LIGA_MX",        # Major league - should investigate
    "NETHERLANDS_EREDIVISIE", # Major league - should investigate
    "NORWAY_ELITESERIEN",    # Applied fixes but no improvement
    "POLAND_EKSTRAKLASA",    # Applied fixes but no improvement
    "PORTUGAL_LIGA_NOS",     # Applied fixes but no improvement
    "SPAIN_LA_LIGA",         # Major league - should investigate
    "SWEDEN_ALLSVENSKAN"     # Applied fixes but no improvement
]

# ============================================================================
# INVESTIGATION FUNCTIONS
# ============================================================================

def deep_analyze_league(league_name: str) -> Dict:
    """Perform deep analysis of a problematic league."""
    print(f"\n🔍 DEEP ANALYSIS: {league_name}")
    print("=" * 60)
    
    analysis = {
        'league_name': league_name,
        'teams_without_stats': [],
        'teams_with_stats': [],
        'circular_references': [],
        'complex_mappings': [],
        'csv_teams': [],
        'potential_issues': []
    }
    
    with get_database() as db:
        # Get all teams in the league
        teams = db.execute_query('''
            SELECT t.team_name, t.team_id, t.canonical_team_id, 
                   t2.team_name as canonical_name,
                   COUNT(ts.team_id) as stat_count
            FROM teams t
            LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            GROUP BY t.team_name, t.team_id, t.canonical_team_id, t2.team_name
            ORDER BY stat_count DESC, t.team_name
        ''', (league_name,))
        
        if teams.empty:
            analysis['potential_issues'].append("No teams found in league")
            return analysis
        
        print(f"📊 Found {len(teams)} teams")
        
        # Categorize teams
        for _, team in teams.iterrows():
            team_name = team['team_name']
            team_id = team['team_id']
            canonical_id = team['canonical_team_id']
            canonical_name = team['canonical_name']
            stat_count = team['stat_count']
            
            team_info = {
                'name': team_name,
                'id': team_id,
                'canonical_id': canonical_id,
                'canonical_name': canonical_name,
                'stat_count': stat_count
            }
            
            if stat_count > 0:
                analysis['teams_with_stats'].append(team_info)
                print(f"   ✅ {team_name} ({stat_count} stats)")
                
                # Check for circular references
                if team_id != canonical_id:
                    analysis['circular_references'].append(team_info)
                    print(f"      ⚠️  Points to {canonical_name} instead of self")
            else:
                analysis['teams_without_stats'].append(team_info)
                print(f"   ❌ {team_name} → {canonical_name}")
                
                # Check for complex mapping chains
                if canonical_name and canonical_name != team_name:
                    # Check if canonical team has stats
                    canonical_stats = db.execute_query(
                        'SELECT COUNT(*) as count FROM team_stats WHERE team_id = ?',
                        (canonical_id,)
                    )
                    canonical_count = canonical_stats.iloc[0]['count'] if not canonical_stats.empty else 0
                    
                    if canonical_count == 0:
                        analysis['complex_mappings'].append({
                            'source': team_name,
                            'target': canonical_name,
                            'issue': 'Points to team without stats'
                        })
                        print(f"      🚨 {canonical_name} also has no stats!")
        
        # Check CSV file
        csv_path = f"data/raw/{league_name}/{league_name}_team_stats.csv"
        if os.path.exists(csv_path):
            try:
                with open(csv_path, 'r') as f:
                    lines = f.readlines()
                    if len(lines) > 1:  # Has header + data
                        for line in lines[1:11]:  # First 10 data lines
                            if ',' in line:
                                team_name = line.split(',')[0].strip().strip('"')
                                analysis['csv_teams'].append(team_name)
                print(f"📄 CSV file exists with {len(lines)-1} teams")
                print(f"   Sample teams: {analysis['csv_teams'][:5]}")
            except Exception as e:
                analysis['potential_issues'].append(f"CSV read error: {e}")
        else:
            analysis['potential_issues'].append("No CSV file found")
            print(f"❌ No CSV file found at {csv_path}")
    
    return analysis

def find_name_mismatches(analysis: Dict) -> List[str]:
    """Find potential name mismatches between database and CSV."""
    issues = []
    
    csv_teams = set(analysis['csv_teams'])
    db_teams_without_stats = {team['name'] for team in analysis['teams_without_stats']}
    db_teams_with_stats = {team['name'] for team in analysis['teams_with_stats']}
    
    # Find CSV teams not in database
    csv_not_in_db = csv_teams - db_teams_with_stats - db_teams_without_stats
    if csv_not_in_db:
        issues.append(f"CSV teams not in database: {list(csv_not_in_db)[:5]}")
    
    # Find database teams not in CSV
    db_not_in_csv = db_teams_without_stats - csv_teams
    if db_not_in_csv:
        issues.append(f"Database teams not in CSV: {list(db_not_in_csv)[:5]}")
    
    # Find potential matches using fuzzy matching
    potential_matches = []
    for db_team in db_teams_without_stats:
        for csv_team in csv_teams:
            # Simple similarity check
            if (db_team.lower() in csv_team.lower() or 
                csv_team.lower() in db_team.lower() or
                abs(len(db_team) - len(csv_team)) <= 3):
                potential_matches.append(f"{db_team} ≈ {csv_team}")
    
    if potential_matches:
        issues.append(f"Potential matches: {potential_matches[:3]}")
    
    return issues

def suggest_fixes(analysis: Dict) -> List[str]:
    """Suggest specific fixes for the league."""
    suggestions = []
    
    # Fix circular references
    if analysis['circular_references']:
        suggestions.append(f"Fix {len(analysis['circular_references'])} circular references (teams with stats pointing elsewhere)")
    
    # Fix complex mappings
    if analysis['complex_mappings']:
        suggestions.append(f"Fix {len(analysis['complex_mappings'])} complex mappings (teams pointing to teams without stats)")
    
    # Data collection issues
    if 'No CSV file found' in analysis['potential_issues']:
        suggestions.append("Data collection issue: No CSV file - need to scrape team stats")
    
    # Name matching issues
    name_issues = find_name_mismatches(analysis)
    if name_issues:
        suggestions.extend([f"Name mismatch: {issue}" for issue in name_issues[:2]])
    
    return suggestions

def investigate_edge_cases():
    """Investigate all edge case leagues."""
    print(f"🔍 INVESTIGATING {len(EDGE_CASE_LEAGUES)} EDGE CASE LEAGUES")
    print("=" * 80)
    print("These leagues applied fixes but showed no improvement")
    
    all_analyses = []
    
    for i, league_name in enumerate(EDGE_CASE_LEAGUES, 1):
        print(f"\n[{i:2d}/{len(EDGE_CASE_LEAGUES)}] {league_name}")
        
        try:
            analysis = deep_analyze_league(league_name)
            suggestions = suggest_fixes(analysis)
            analysis['suggestions'] = suggestions
            all_analyses.append(analysis)
            
            # Show brief summary
            teams_without = len(analysis['teams_without_stats'])
            teams_with = len(analysis['teams_with_stats'])
            total = teams_without + teams_with
            
            if total > 0:
                success_rate = (teams_with / total) * 100
                print(f"   📊 {teams_with}/{total} teams working ({success_rate:.1f}%)")
                
                if suggestions:
                    print(f"   💡 Suggestions: {len(suggestions)}")
                    for suggestion in suggestions[:2]:
                        print(f"      - {suggestion}")
            
        except Exception as e:
            print(f"   ❌ Error analyzing {league_name}: {e}")
    
    return all_analyses

def print_investigation_summary(analyses: List[Dict]):
    """Print summary of investigation results."""
    print(f"\n🎯 EDGE CASE INVESTIGATION SUMMARY")
    print("=" * 60)
    
    # Categorize issues
    circular_ref_leagues = [a for a in analyses if a.get('circular_references')]
    complex_mapping_leagues = [a for a in analyses if a.get('complex_mappings')]
    no_csv_leagues = [a for a in analyses if 'No CSV file found' in a.get('potential_issues', [])]
    name_mismatch_leagues = [a for a in analyses if any('Name mismatch' in s for s in a.get('suggestions', []))]
    
    print(f"📊 ISSUE CATEGORIES:")
    print(f"   Circular references: {len(circular_ref_leagues)} leagues")
    print(f"   Complex mappings: {len(complex_mapping_leagues)} leagues")
    print(f"   Missing CSV files: {len(no_csv_leagues)} leagues")
    print(f"   Name mismatches: {len(name_mismatch_leagues)} leagues")
    
    # Show top priority fixes
    print(f"\n🚨 TOP PRIORITY FIXES:")
    
    if circular_ref_leagues:
        print(f"\n1. CIRCULAR REFERENCES ({len(circular_ref_leagues)} leagues):")
        for analysis in circular_ref_leagues[:5]:
            count = len(analysis['circular_references'])
            print(f"   - {analysis['league_name']}: {count} teams with stats pointing elsewhere")
    
    if complex_mapping_leagues:
        print(f"\n2. COMPLEX MAPPINGS ({len(complex_mapping_leagues)} leagues):")
        for analysis in complex_mapping_leagues[:5]:
            count = len(analysis['complex_mappings'])
            print(f"   - {analysis['league_name']}: {count} teams pointing to teams without stats")
    
    if no_csv_leagues:
        print(f"\n3. MISSING DATA ({len(no_csv_leagues)} leagues):")
        for analysis in no_csv_leagues[:5]:
            print(f"   - {analysis['league_name']}: No CSV file - data collection needed")
    
    # Show actionable next steps
    print(f"\n💡 ACTIONABLE NEXT STEPS:")
    print(f"1. Fix circular references in {len(circular_ref_leagues)} leagues")
    print(f"2. Resolve complex mappings in {len(complex_mapping_leagues)} leagues")
    print(f"3. Investigate data collection for {len(no_csv_leagues)} leagues")
    print(f"4. Apply name matching fixes for {len(name_mismatch_leagues)} leagues")

def main():
    """Main investigation function."""
    print(f"🔍 EDGE CASE LEAGUE INVESTIGATION")
    print("=" * 60)
    print("Focusing on 40 leagues that applied fixes but showed no improvement")
    
    # Run investigation
    analyses = investigate_edge_cases()
    
    # Print summary
    print_investigation_summary(analyses)
    
    print(f"\n🎯 INVESTIGATION COMPLETE!")
    print("These edge cases represent the most complex team mapping issues.")
    print("Manual fixes will be needed for optimal results.")

if __name__ == "__main__":
    main()
