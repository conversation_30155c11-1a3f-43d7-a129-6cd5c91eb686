#!/usr/bin/env python3
"""
Enhanced Terminal Prediction Interface

An interactive command-line interface for making football match predictions
with improved user experience, real-time model updates, and comprehensive analysis.
"""

import sys
import os
import argparse
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import pandas as pd

# Add src to path
sys.path.append('src')

from data_loading import get_available_leagues, load_data, validate_and_convert_data
from feature_engineering import prepare_features, engineer_features
try:
    from model_training import train_model, analyze_feature_importance
except ImportError:
    # Fallback if model_training module has issues
    def train_model(*args, **kwargs):
        print("⚠️  Model training module not available. Using fallback.")
        return {}
    def analyze_feature_importance(*args, **kwargs):
        return {}
from prediction import predict_match, save_predictions_to_excel
from database.football_db import get_database
from utils import setup_logging, standardize_team_name

class TerminalPredictor:
    """Enhanced terminal prediction interface with improved UX."""
    
    def __init__(self):
        self.models = {}
        self.league_data = {}
        self.available_leagues = []
        self.current_league = None
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging for the terminal predictor."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('terminal_predictor.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def initialize(self):
        """Initialize the prediction system."""
        print("🚀 Enhanced Terminal Football Predictor")
        print("=" * 50)
        
        # Get available leagues
        self.available_leagues = get_available_leagues()
        if not self.available_leagues:
            print("❌ No league data found. Please run data scraping first.")
            return False
            
        print(f"✅ Found {len(self.available_leagues)} leagues available")
        return True
        
    def display_leagues(self):
        """Display available leagues in a formatted way."""
        print("\n📊 Available Leagues:")
        print("-" * 30)
        
        # Group leagues by country for better display
        league_groups = {}
        for league in sorted(self.available_leagues):
            country = league.split('_')[0]
            if country not in league_groups:
                league_groups[country] = []
            league_groups[country].append(league)
            
        for country, leagues in sorted(league_groups.items()):
            print(f"\n🏴 {country.title()}:")
            for i, league in enumerate(leagues, 1):
                league_display = league.replace('_', ' ').title()
                print(f"  {i:2d}. {league_display}")
                
    def select_league(self, league_name: Optional[str] = None) -> bool:
        """Select and load a league for predictions."""
        if league_name:
            if league_name not in self.available_leagues:
                print(f"❌ League '{league_name}' not found.")
                return False
            selected_league = league_name
        else:
            self.display_leagues()
            print(f"\n🎯 Enter league name (or 'list' to see options again):")
            user_input = input("> ").strip()
            
            if user_input.lower() == 'list':
                return self.select_league()
                
            # Try to match user input
            selected_league = self._match_league_name(user_input)
            if not selected_league:
                print(f"❌ Could not find league matching '{user_input}'")
                return False
                
        print(f"\n🔄 Loading {selected_league.replace('_', ' ').title()}...")
        
        try:
            # Load league data
            league_config = self._get_league_config(selected_league)
            data, league_config = load_data(selected_league, league_config)
            
            if not self._validate_league_data(data, selected_league):
                return False
                
            results, team_stats, league_stats, h2h_stats, league_table = data
            
            # Prepare features and train models
            print("🧠 Training prediction models...")
            prepared_data = self._prepare_league_features(
                results, team_stats, league_stats, h2h_stats, league_table, league_config
            )
            
            if not prepared_data:
                print("❌ Failed to prepare features")
                return False
                
            X, y_dict, label_encoders, mappings = prepared_data
            
            # Train models
            models = train_model(X, y_dict, label_encoders)
            if not models:
                print("❌ Failed to train models")
                return False
                
            # Store league data
            self.current_league = selected_league
            self.models = models
            self.league_data = {
                'team_stats': team_stats,
                'league_stats': league_stats,
                'h2h_stats': h2h_stats,
                'league_table': league_table,
                'mappings': mappings,
                'label_encoders': label_encoders,
                'results': results
            }
            
            print(f"✅ {selected_league.replace('_', ' ').title()} loaded successfully!")
            print(f"📊 Trained {len(models)} prediction models")
            
            # Display model performance
            self._display_model_performance()
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading league: {str(e)}")
            self.logger.error(f"Error loading {selected_league}: {str(e)}")
            return False
            
    def _match_league_name(self, user_input: str) -> Optional[str]:
        """Match user input to available league names."""
        user_input = user_input.upper().replace(' ', '_')
        
        # Exact match
        if user_input in self.available_leagues:
            return user_input
            
        # Partial match
        matches = [league for league in self.available_leagues if user_input in league]
        if len(matches) == 1:
            return matches[0]
        elif len(matches) > 1:
            print(f"🤔 Multiple matches found for '{user_input}':")
            for i, match in enumerate(matches, 1):
                print(f"  {i}. {match.replace('_', ' ').title()}")
            try:
                choice = int(input("Select number: ")) - 1
                if 0 <= choice < len(matches):
                    return matches[choice]
            except (ValueError, IndexError):
                pass
                
        return None
        
    def _get_league_config(self, league_name: str) -> Dict:
        """Get configuration for a league."""
        try:
            from src.scrapers.config import LEAGUE_CONFIGS
            return LEAGUE_CONFIGS.get(league_name, {})
        except ImportError:
            return {}
            
    def _validate_league_data(self, data, league_name: str) -> bool:
        """Validate loaded league data."""
        if not data or len(data) != 5:
            print(f"❌ Invalid data structure for {league_name}")
            return False
            
        results, team_stats, league_stats, h2h_stats, league_table = data
        
        if team_stats.empty:
            print(f"❌ No team statistics found for {league_name}")
            return False
            
        print(f"📊 Data loaded: {len(team_stats)} teams, {len(results)} matches")
        return True
        
    def _prepare_league_features(self, results, team_stats, league_stats, h2h_stats, league_table, league_config):
        """Prepare features for model training."""
        try:
            # Get column mappings - use a simple default mapping if import fails
            try:
                from src.config.model_training import get_column_mappings
                mappings = get_column_mappings()
                combined_mapping = {**mappings["team_stats"], **mappings["h2h_stats"]}
            except ImportError:
                # Fallback to basic mappings
                combined_mapping = {}
            
            # Prepare team mappings
            team_name_mapping = league_config.get("TEAM_NAME_MAPPING", {})
            
            # Prepare features
            prepared_data = prepare_features(
                results, team_stats, league_stats, h2h_stats, league_table,
                combined_mapping, team_name_mapping
            )
            
            if not prepared_data:
                return None
                
            X, y_dict, label_encoders = prepared_data
            return X, y_dict, label_encoders, combined_mapping
            
        except Exception as e:
            self.logger.error(f"Error preparing features: {str(e)}")
            return None
            
    def _display_model_performance(self):
        """Display performance metrics for trained models."""
        print("\n📈 Model Performance:")
        print("-" * 30)
        
        for pred_type, model_info in self.models.items():
            accuracy = model_info.get('accuracy', 0)
            model_type = "Neural Network" if 'tensorflow' in str(type(model_info['model'])) else "Random Forest"
            print(f"  {pred_type.replace('_', ' ').title()}: {accuracy:.1%} ({model_type})")
            
    def get_available_teams(self) -> List[str]:
        """Get list of available teams in current league."""
        if not self.current_league or 'team_stats' not in self.league_data:
            return []
        return sorted(self.league_data['team_stats']['Team'].unique().tolist())
        
    def predict_match_interactive(self):
        """Interactive match prediction interface."""
        if not self.current_league:
            print("❌ No league selected. Please select a league first.")
            return
            
        teams = self.get_available_teams()
        print(f"\n⚽ Match Prediction - {self.current_league.replace('_', ' ').title()}")
        print("=" * 50)
        
        # Get home team
        print(f"\n🏠 Available teams ({len(teams)} total):")
        for i, team in enumerate(teams[:10], 1):  # Show first 10
            print(f"  {i:2d}. {team}")
        if len(teams) > 10:
            print(f"  ... and {len(teams) - 10} more")
            
        home_team = self._select_team("🏠 Home team", teams)
        if not home_team:
            return
            
        away_team = self._select_team("✈️  Away team", teams, exclude=home_team)
        if not away_team:
            return
            
        # Make prediction
        print(f"\n🔮 Predicting: {home_team} vs {away_team}")
        print("-" * 50)
        
        try:
            prediction_result = self._make_single_prediction(home_team, away_team)
            if prediction_result:
                self._display_prediction_results(home_team, away_team, prediction_result)
            else:
                print("❌ Failed to generate prediction")
                
        except Exception as e:
            print(f"❌ Error making prediction: {str(e)}")
            self.logger.error(f"Prediction error: {str(e)}")
            
    def _select_team(self, prompt: str, teams: List[str], exclude: str = None) -> Optional[str]:
        """Interactive team selection."""
        while True:
            print(f"\n{prompt} (type name or 'search <term>'):")
            user_input = input("> ").strip()
            
            if user_input.lower().startswith('search '):
                search_term = user_input[7:].lower()
                matches = [team for team in teams if search_term in team.lower()]
                if matches:
                    print(f"🔍 Found {len(matches)} matches:")
                    for i, team in enumerate(matches[:10], 1):
                        print(f"  {i:2d}. {team}")
                    continue
                else:
                    print("❌ No matches found")
                    continue
                    
            # Try to match team name
            selected_team = self._match_team_name(user_input, teams)
            if selected_team:
                if exclude and selected_team == exclude:
                    print("❌ Cannot select the same team twice")
                    continue
                return selected_team
            else:
                print(f"❌ Team '{user_input}' not found. Try 'search <term>' to find teams.")
                
    def _match_team_name(self, user_input: str, teams: List[str]) -> Optional[str]:
        """Match user input to team name."""
        # Exact match (case insensitive)
        for team in teams:
            if user_input.lower() == team.lower():
                return team
                
        # Partial match
        matches = [team for team in teams if user_input.lower() in team.lower()]
        if len(matches) == 1:
            return matches[0]
        elif len(matches) > 1:
            print(f"🤔 Multiple matches found:")
            for i, match in enumerate(matches[:5], 1):
                print(f"  {i}. {match}")
            try:
                choice = int(input("Select number: ")) - 1
                if 0 <= choice < len(matches):
                    return matches[choice]
            except (ValueError, IndexError):
                pass
                
        return None

    def _make_single_prediction(self, home_team: str, away_team: str) -> Optional[Dict]:
        """Make a prediction for a single match."""
        try:
            # Get average goals per match
            avg_goals = float(
                self.league_data['league_stats'][
                    self.league_data['league_stats']['Stat'] == 'avg_goals_per_match'
                ]['Value'].values[0]
            )

            # Make prediction using the core prediction function
            pred_results, error_message, correct_scores = predict_match(
                self.models,
                home_team,
                away_team,
                self.league_data['team_stats'],
                self.league_data['league_stats'],
                self.league_data['h2h_stats'],
                self.league_data['league_table'],
                self.league_data['mappings'],
                self.models['three_way']['feature_names'],
                avg_goals,
                label_encoders=self.league_data['label_encoders'],
                bias_correction=0.05,
                log_features=True
            )

            if error_message:
                print(f"❌ Prediction error: {error_message}")
                return None

            return {
                'predictions': pred_results.get('main_predictions', {}),
                'correct_scores': correct_scores
            }

        except Exception as e:
            self.logger.error(f"Error in _make_single_prediction: {str(e)}")
            return None

    def _display_prediction_results(self, home_team: str, away_team: str, results: Dict):
        """Display prediction results in a formatted way."""
        predictions = results['predictions']
        correct_scores = results['correct_scores']

        print(f"\n🎯 PREDICTION RESULTS")
        print("=" * 50)
        print(f"🏠 Home: {home_team}")
        print(f"✈️  Away: {away_team}")
        print(f"🕒 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Main predictions
        print(f"\n📊 MAIN PREDICTIONS:")
        print("-" * 30)

        for pred_type, pred_data in predictions.items():
            if isinstance(pred_data, dict) and 'prediction' in pred_data:
                prediction = pred_data['prediction']
                probabilities = pred_data.get('probabilities', {})

                print(f"\n🎲 {pred_type.replace('_', ' ').title()}:")
                print(f"   Prediction: {prediction}")

                if probabilities:
                    print("   Probabilities:")
                    for outcome, prob in probabilities.items():
                        if isinstance(prob, (int, float)):
                            print(f"     {outcome}: {prob:.1%}")

        # Correct scores
        if correct_scores:
            print(f"\n⚽ MOST LIKELY SCORES:")
            print("-" * 30)

            # Sort scores by probability
            sorted_scores = sorted(correct_scores.items(), key=lambda x: x[1], reverse=True)

            for i, (score, prob) in enumerate(sorted_scores[:5], 1):
                print(f"   {i}. {score}: {prob:.1%}")

        # Confidence analysis
        self._display_confidence_analysis(predictions)

    def _display_confidence_analysis(self, predictions: Dict):
        """Display confidence analysis for predictions."""
        print(f"\n🎯 CONFIDENCE ANALYSIS:")
        print("-" * 30)

        confidence_scores = []

        for pred_type, pred_data in predictions.items():
            if isinstance(pred_data, dict) and 'probabilities' in pred_data:
                probabilities = pred_data['probabilities']
                if probabilities:
                    # Calculate confidence as max probability
                    max_prob = max(probabilities.values()) if probabilities.values() else 0
                    confidence_scores.append((pred_type, max_prob))

        if confidence_scores:
            avg_confidence = sum(score for _, score in confidence_scores) / len(confidence_scores)

            print(f"   Average Confidence: {avg_confidence:.1%}")

            if avg_confidence >= 0.7:
                print("   🟢 High Confidence Prediction")
            elif avg_confidence >= 0.5:
                print("   🟡 Medium Confidence Prediction")
            else:
                print("   🔴 Low Confidence Prediction")

            print("\n   Individual Confidences:")
            for pred_type, confidence in confidence_scores:
                print(f"     {pred_type.replace('_', ' ').title()}: {confidence:.1%}")

    def batch_predict(self, matches: List[Tuple[str, str]]):
        """Make predictions for multiple matches."""
        if not self.current_league:
            print("❌ No league selected. Please select a league first.")
            return

        print(f"\n🔄 Batch Prediction - {len(matches)} matches")
        print("=" * 50)

        all_predictions = {}

        for i, (home_team, away_team) in enumerate(matches, 1):
            print(f"\n[{i}/{len(matches)}] {home_team} vs {away_team}")

            try:
                prediction_result = self._make_single_prediction(home_team, away_team)
                if prediction_result:
                    match_key = f"{home_team} vs {away_team}"
                    all_predictions[match_key] = {
                        'home_team': home_team,
                        'away_team': away_team,
                        'predictions': prediction_result['predictions'],
                        'correct_scores': prediction_result['correct_scores'],
                        'timestamp': datetime.now().isoformat()
                    }
                    print("   ✅ Prediction completed")
                else:
                    print("   ❌ Prediction failed")

            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
                continue

        # Save to Excel
        if all_predictions:
            try:
                filename = f"{self.current_league}_batch_predictions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                save_predictions_to_excel(all_predictions, self.current_league, filename)
                print(f"\n💾 Predictions saved to: {filename}")
            except Exception as e:
                print(f"❌ Error saving predictions: {str(e)}")

        return all_predictions

    def interactive_menu(self):
        """Main interactive menu."""
        while True:
            print(f"\n🎮 TERMINAL PREDICTOR MENU")
            print("=" * 30)

            if self.current_league:
                print(f"📊 Current League: {self.current_league.replace('_', ' ').title()}")
            else:
                print("📊 No league selected")

            print("\nOptions:")
            print("  1. Select League")
            print("  2. Predict Single Match")
            print("  3. Batch Predict")
            print("  4. Show League Info")
            print("  5. Show Available Teams")
            print("  6. Model Performance")
            print("  7. Exit")

            try:
                choice = input("\nSelect option (1-7): ").strip()

                if choice == '1':
                    self.select_league()
                elif choice == '2':
                    self.predict_match_interactive()
                elif choice == '3':
                    self._batch_predict_interactive()
                elif choice == '4':
                    self._show_league_info()
                elif choice == '5':
                    self._show_available_teams()
                elif choice == '6':
                    self._display_model_performance()
                elif choice == '7':
                    print("👋 Goodbye!")
                    break
                else:
                    print("❌ Invalid option. Please select 1-7.")

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {str(e)}")

    def _batch_predict_interactive(self):
        """Interactive batch prediction setup."""
        if not self.current_league:
            print("❌ No league selected. Please select a league first.")
            return

        print("\n📝 Batch Prediction Setup")
        print("Enter matches in format: 'Home Team vs Away Team'")
        print("Type 'done' when finished, 'cancel' to abort")

        matches = []
        teams = self.get_available_teams()

        while True:
            match_input = input(f"\nMatch {len(matches) + 1} (or 'done'/'cancel'): ").strip()

            if match_input.lower() == 'done':
                break
            elif match_input.lower() == 'cancel':
                return

            # Parse match input
            if ' vs ' in match_input:
                home_team, away_team = match_input.split(' vs ', 1)
                home_team = home_team.strip()
                away_team = away_team.strip()

                # Validate teams
                home_match = self._match_team_name(home_team, teams)
                away_match = self._match_team_name(away_team, teams)

                if home_match and away_match:
                    matches.append((home_match, away_match))
                    print(f"   ✅ Added: {home_match} vs {away_match}")
                else:
                    print("   ❌ One or both teams not found")
            else:
                print("   ❌ Invalid format. Use: 'Home Team vs Away Team'")

        if matches:
            self.batch_predict(matches)
        else:
            print("❌ No valid matches entered")

    def _show_league_info(self):
        """Show information about current league."""
        if not self.current_league:
            print("❌ No league selected")
            return

        print(f"\n📊 League Information: {self.current_league.replace('_', ' ').title()}")
        print("=" * 50)

        team_stats = self.league_data.get('team_stats')
        if team_stats is not None:
            print(f"🏟️  Teams: {len(team_stats)}")

        results = self.league_data.get('results')
        if results is not None:
            print(f"⚽ Matches: {len(results)}")

        league_stats = self.league_data.get('league_stats')
        if league_stats is not None:
            avg_goals = league_stats[league_stats['Stat'] == 'avg_goals_per_match']['Value'].values
            if len(avg_goals) > 0:
                print(f"📈 Avg Goals/Match: {float(avg_goals[0]):.2f}")

        print(f"🤖 Models Trained: {len(self.models)}")

    def _show_available_teams(self):
        """Show all available teams in current league."""
        if not self.current_league:
            print("❌ No league selected")
            return

        teams = self.get_available_teams()
        print(f"\n🏟️  Available Teams in {self.current_league.replace('_', ' ').title()}:")
        print("=" * 50)

        for i, team in enumerate(teams, 1):
            print(f"  {i:2d}. {team}")

        print(f"\nTotal: {len(teams)} teams")


def main():
    """Main function for terminal predictor."""
    parser = argparse.ArgumentParser(
        description="Enhanced Terminal Football Predictor",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python terminal_predictor.py                    # Interactive mode
  python terminal_predictor.py --league ENGLAND_PREMIER_LEAGUE  # Load specific league
  python terminal_predictor.py --predict "Arsenal vs Chelsea"   # Quick prediction
        """
    )

    parser.add_argument(
        '--league',
        type=str,
        help='League to load automatically'
    )
    parser.add_argument(
        '--predict',
        type=str,
        help='Quick prediction in format "Home Team vs Away Team"'
    )
    parser.add_argument(
        '--batch-file',
        type=str,
        help='File containing matches to predict (one per line)'
    )

    args = parser.parse_args()

    # Initialize predictor
    predictor = TerminalPredictor()
    if not predictor.initialize():
        return 1

    # Handle command line arguments
    if args.league:
        if not predictor.select_league(args.league):
            return 1

    if args.predict:
        if not predictor.current_league:
            print("❌ No league loaded. Use --league to specify a league.")
            return 1

        # Parse prediction
        if ' vs ' in args.predict:
            home_team, away_team = args.predict.split(' vs ', 1)
            teams = predictor.get_available_teams()

            home_match = predictor._match_team_name(home_team.strip(), teams)
            away_match = predictor._match_team_name(away_team.strip(), teams)

            if home_match and away_match:
                result = predictor._make_single_prediction(home_match, away_match)
                if result:
                    predictor._display_prediction_results(home_match, away_match, result)
                return 0
            else:
                print("❌ One or both teams not found")
                return 1
        else:
            print("❌ Invalid format. Use: 'Home Team vs Away Team'")
            return 1

    if args.batch_file:
        if not predictor.current_league:
            print("❌ No league loaded. Use --league to specify a league.")
            return 1

        try:
            with open(args.batch_file, 'r') as f:
                matches = []
                teams = predictor.get_available_teams()

                for line in f:
                    line = line.strip()
                    if ' vs ' in line:
                        home_team, away_team = line.split(' vs ', 1)
                        home_match = predictor._match_team_name(home_team.strip(), teams)
                        away_match = predictor._match_team_name(away_team.strip(), teams)

                        if home_match and away_match:
                            matches.append((home_match, away_match))

                if matches:
                    predictor.batch_predict(matches)
                    return 0
                else:
                    print("❌ No valid matches found in file")
                    return 1

        except FileNotFoundError:
            print(f"❌ File not found: {args.batch_file}")
            return 1

    # Interactive mode
    predictor.interactive_menu()
    return 0


if __name__ == "__main__":
    sys.exit(main())
