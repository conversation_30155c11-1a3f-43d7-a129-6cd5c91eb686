#!/usr/bin/env python3
"""
Fix Spanish Team Canonical Mappings

This script fixes canonical team mappings for Spanish teams where multiple
variations exist but don't point to the same canonical team, causing
head-to-head data to not be found.
"""

import sys
import sqlite3
import os

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

def fix_spanish_team_mappings():
    """Fix canonical mappings for Spanish teams."""
    print("🔧 Fixing Spanish Team Canonical Mappings")
    print("=" * 50)
    
    # Define the mappings that need to be fixed
    # Format: (short_name, canonical_name, league)
    mappings_to_fix = [
        ("Alaves", "Deportivo Alavés", "SPAIN_LA_LIGA"),
        # Add more mappings here if needed
    ]
    
    with get_database() as db:
        for short_name, canonical_name, league in mappings_to_fix:
            print(f"\n🔄 Processing: {short_name} -> {canonical_name}")
            
            # Get the team IDs
            short_team = db.execute_query('''
                SELECT t.team_id, t.canonical_team_id
                FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (short_name, league))
            
            canonical_team = db.execute_query('''
                SELECT t.team_id
                FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (canonical_name, league))
            
            if short_team.empty:
                print(f"   ❌ Short team '{short_name}' not found")
                continue
                
            if canonical_team.empty:
                print(f"   ❌ Canonical team '{canonical_name}' not found")
                continue
            
            short_team_id = short_team.iloc[0]['team_id']
            current_canonical_id = short_team.iloc[0]['canonical_team_id']
            new_canonical_id = canonical_team.iloc[0]['team_id']
            
            print(f"   📋 Short team ID: {short_team_id}")
            print(f"   📋 Current canonical ID: {current_canonical_id}")
            print(f"   📋 New canonical ID: {new_canonical_id}")
            
            if current_canonical_id == new_canonical_id:
                print(f"   ✅ Already correctly mapped")
                continue
            
            # Update the canonical mapping
            try:
                db.conn.execute(
                    "UPDATE teams SET canonical_team_id = ? WHERE team_id = ?",
                    (new_canonical_id, short_team_id)
                )
                db.conn.commit()
                print(f"   ✅ Updated canonical mapping successfully")
                
                # Verify the update
                verification = db.execute_query('''
                    SELECT canonical_team_id FROM teams WHERE team_id = ?
                ''', (short_team_id,))
                
                if not verification.empty and verification.iloc[0]['canonical_team_id'] == new_canonical_id:
                    print(f"   ✅ Verification successful")
                else:
                    print(f"   ❌ Verification failed")
                    
            except Exception as e:
                print(f"   ❌ Error updating mapping: {e}")

def test_fixed_mappings():
    """Test that the fixed mappings work for H2H queries."""
    print("\n🧪 Testing Fixed Mappings")
    print("=" * 30)
    
    with get_database() as db:
        # Test Alaves vs Athletic Bilbao
        print("Testing: Alaves vs Athletic Bilbao")
        h2h = db.get_head_to_head_stats('Alaves', 'Athletic Bilbao', 'SPAIN_LA_LIGA')
        
        if not h2h.empty:
            row = h2h.iloc[0]
            print(f"✅ H2H data found: {row['total_matches']} matches")
            print(f"   Home team in data: {row['home_team']}")
            print(f"   Away team in data: {row['away_team']}")
        else:
            print("❌ Still no H2H data found")
        
        # Test the dropdown (should now show only canonical names)
        teams = db.get_teams('SPAIN_LA_LIGA')
        alaves_teams = [t for t in teams['team_name'].tolist() if 'Alaves' in t or 'Alavés' in t]
        print(f"\nTeams in dropdown: {alaves_teams}")

def main():
    """Main function."""
    print("🇪🇸 Spanish Team Mapping Fix")
    print("=" * 40)
    
    # Show current state
    print("\n📊 Current State:")
    with get_database() as db:
        h2h_before = db.get_head_to_head_stats('Alaves', 'Athletic Bilbao', 'SPAIN_LA_LIGA')
        print(f"Alaves vs Athletic Bilbao H2H: {len(h2h_before)} results")
        
        teams = db.get_teams('SPAIN_LA_LIGA')
        alaves_teams = [t for t in teams['team_name'].tolist() if 'Alaves' in t or 'Alavés' in t]
        print(f"Alaves teams in dropdown: {alaves_teams}")
    
    # Apply fixes
    fix_spanish_team_mappings()
    
    # Test fixes
    test_fixed_mappings()
    
    print("\n🎉 Spanish team mapping fix complete!")
    print("The head-to-head functionality should now work correctly.")

if __name__ == "__main__":
    main()
