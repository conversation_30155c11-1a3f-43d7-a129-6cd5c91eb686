#!/usr/bin/env python3
"""
Run High Priority League Fixes

This script runs fixes for leagues that are closest to 90% success rate.
These are the best candidates for quick improvements.

Priority: Leagues with 80-89% success rate
"""

import sys
import subprocess
import os
from typing import List, Tuple, Dict

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

def get_current_league_status(league_name: str) -> Tuple[int, int, float]:
    """Get current status of a league."""
    with get_database() as db:
        teams = db.execute_query('''
            SELECT t.team_name FROM teams t
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
        ''', (league_name,))
        
        if teams.empty:
            return 0, 0, 0.0
        
        working_teams = 0
        total_teams = len(teams)
        
        for _, team in teams.iterrows():
            stats = db.get_team_stats(team['team_name'], league_name)
            if not stats.empty:
                working_teams += 1
        
        success_rate = (working_teams / total_teams) * 100 if total_teams > 0 else 0
        return working_teams, total_teams, success_rate

def run_fix_script(script_name: str) -> bool:
    """Run a fix script and return success status."""
    if not os.path.exists(script_name):
        print(f"Script {script_name} not found")
        return False
    
    try:
        print(f"\nRunning {script_name}...")
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"{script_name} completed successfully")
            return True
        else:
            print(f"{script_name} failed with return code {result.returncode}")
            if result.stderr:
                print(f"Error: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"{script_name} timed out")
        return False
    except Exception as e:
        print(f"Error running {script_name}: {e}")
        return False

def create_generic_fix_script(league_name: str, league_display_name: str) -> str:
    """Create a generic fix script for a league."""
    script_name = f"fix_{league_name}.py"
    
    if os.path.exists(script_name):
        return script_name  # Already exists
    
    # Create a basic fix script based on the template
    script_content = f'''#!/usr/bin/env python3
"""
{league_display_name} Team Mapping Fix

Auto-generated fix script for leagues under 90% success rate.
"""

import sys
import importlib.util
from typing import Dict, List, Tuple

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

LEAGUE_NAME = "{league_name}"
LEAGUE_DISPLAY_NAME = "{league_display_name}"

def load_league_config() -> Dict[str, str]:
    """Load the league config mappings if available."""
    config_path = f"src/scrapers/league_configs/{{LEAGUE_NAME}}.py"
    
    try:
        spec = importlib.util.spec_from_file_location("config", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        
        current_config = getattr(config_module, 'CURRENT_CONFIG', None)
        if current_config and 'TEAM_NAME_MAPPING' in current_config:
            return current_config['TEAM_NAME_MAPPING']
        
        team_mapping = getattr(config_module, 'TEAM_NAME_MAPPING', None)
        if team_mapping:
            return team_mapping
            
        return {{}}
    except Exception as e:
        print(f"⚠️  No config file found or error loading: {{e}}")
        return {{}}

def analyze_current_state():
    """Analyze the current state of the league."""
    print(f"🔍 ANALYZING {{LEAGUE_DISPLAY_NAME}} CURRENT STATE")
    print("=" * 60)
    
    with get_database() as db:
        teams = db.execute_query(\'\'\'
            SELECT t.team_name, t.team_id, t.canonical_team_id, 
                   t2.team_name as canonical_name
            FROM teams t
            LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY t.team_name
        \'\'\', (LEAGUE_NAME,))
        
        if teams.empty:
            print(f"❌ No teams found for {{LEAGUE_NAME}}")
            return {{}}
        
        print(f"📊 Found {{len(teams)}} teams in {{LEAGUE_DISPLAY_NAME}}")
        
        teams_info = {{}}
        working_teams = 0
        
        for _, team in teams.iterrows():
            team_name = team['team_name']
            team_id = team['team_id']
            canonical_name = team['canonical_name']
            
            stats = db.get_team_stats(team_name, LEAGUE_NAME)
            has_stats = not stats.empty
            
            if has_stats:
                working_teams += 1
            
            teams_info[team_name] = {{
                'team_id': team_id,
                'canonical_name': canonical_name,
                'has_stats': has_stats,
                'canonical_id': team['canonical_team_id']
            }}
            
            status = "✅" if has_stats else "❌"
            canonical_info = f" → {{canonical_name}}" if canonical_name != team_name else ""
            print(f"   {{status}} {{team_name}}{{canonical_info}}")
        
        success_rate = (working_teams / len(teams)) * 100
        print(f"\\n📊 Current success rate: {{working_teams}}/{{len(teams)}} ({{success_rate:.1f}}%)")
        
        return teams_info

def apply_config_mappings(config_mappings: Dict[str, str]):
    """Apply config mappings if available."""
    if not config_mappings:
        print("⚠️  No config mappings to apply")
        return 0
    
    print(f"\\n🔧 APPLYING CONFIG MAPPINGS")
    print("=" * 50)
    
    fixes_applied = 0
    
    with get_database() as db:
        for current_team, old_team in config_mappings.items():
            print(f"\\n📋 Processing: {{current_team}} ← {{old_team}}")
            
            current_id = db.execute_scalar(\'\'\'
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            \'\'\', (current_team, LEAGUE_NAME))
            
            old_id = db.execute_scalar(\'\'\'
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            \'\'\', (old_team, LEAGUE_NAME))
            
            if not current_id or not old_id:
                print(f"   ⚠️  Team not found")
                continue
            
            current_stats = db.get_team_stats(current_team, LEAGUE_NAME)
            old_stats = db.get_team_stats(old_team, LEAGUE_NAME)
            
            if current_stats.empty and old_stats.empty:
                continue
            
            if not current_stats.empty and old_stats.empty:
                target_id, source_id = current_id, old_id
                direction = f"{{old_team}} → {{current_team}}"
            elif current_stats.empty and not old_stats.empty:
                target_id, source_id = old_id, current_id
                direction = f"{{current_team}} → {{old_team}}"
            else:
                continue
            
            try:
                db.conn.execute(
                    'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                    (target_id, source_id)
                )
                print(f"   ✅ Applied mapping: {{direction}}")
                fixes_applied += 1
            except Exception as e:
                print(f"   ❌ Error: {{e}}")
        
        if fixes_applied > 0:
            db.conn.commit()
            print(f"\\n💾 Committed {{fixes_applied}} config-based fixes")
    
    return fixes_applied

def fix_circular_references():
    """Fix teams with stats that point to teams without stats."""
    print(f"\\n🚨 CHECKING FOR CIRCULAR REFERENCES")
    print("=" * 50)

    fixes_applied = 0

    with get_database() as db:
        problematic_teams = db.execute_query(\'\'\'
            SELECT t.team_id, t.team_name, t.canonical_team_id,
                   COUNT(ts.team_id) as stat_count
            FROM teams t
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ? AND t.team_id != t.canonical_team_id
            GROUP BY t.team_id, t.team_name, t.canonical_team_id
            HAVING stat_count > 0
        \'\'\', (LEAGUE_NAME,))

        if problematic_teams.empty:
            print("✅ No circular references found")
            return 0

        print(f"Found {{len(problematic_teams)}} teams with stats pointing to other teams:")

        for _, team in problematic_teams.iterrows():
            team_id = team['team_id']
            team_name = team['team_name']
            stat_count = team['stat_count']

            print(f"\\n🔧 Fixing: {{team_name}} (ID: {{team_id}})")
            print(f"   Has {{stat_count}} stats but points to other team")
            print(f"   Making it canonical (point to itself)")

            db.conn.execute(
                'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                (team_id, team_id)
            )
            fixes_applied += 1

        if fixes_applied > 0:
            db.conn.commit()
            print(f"\\n💾 Committed {{fixes_applied}} circular reference fixes")

    return fixes_applied

def verify_final_state():
    """Verify the final state after all fixes."""
    print(f"\\n📊 FINAL {{LEAGUE_DISPLAY_NAME}} STATUS")
    print("=" * 50)
    
    with get_database() as db:
        teams = db.execute_query(\'\'\'
            SELECT t.team_name, t2.team_name as canonical_name
            FROM teams t
            LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY t.team_name
        \'\'\', (LEAGUE_NAME,))
        
        working_teams = 0
        total_teams = len(teams)
        
        for _, team in teams.iterrows():
            team_name = team['team_name']
            canonical_name = team['canonical_name']
            
            stats = db.get_team_stats(team_name, LEAGUE_NAME)
            has_stats = not stats.empty
            
            if has_stats:
                working_teams += 1
            
            status = "✅" if has_stats else "❌"
            canonical_info = f" → {{canonical_name}}" if canonical_name != team_name else ""
            print(f"   {{status}} {{team_name}}{{canonical_info}}")
        
        success_rate = (working_teams / total_teams) * 100
        print(f"\\n📈 Final Results:")
        print(f"   Working teams: {{working_teams}}/{{total_teams}}")
        print(f"   Success rate: {{success_rate:.1f}}%")
        
        return working_teams, total_teams

def main():
    """Main function."""
    print(f"🏆 {{LEAGUE_DISPLAY_NAME.upper()}} TEAM MAPPING FIX")
    print("=" * 70)
    print(f"Fixing canonical team mappings for {{LEAGUE_DISPLAY_NAME}}")
    
    teams_info = analyze_current_state()
    if not teams_info:
        return
    
    config_mappings = load_league_config()
    config_fixes = apply_config_mappings(config_mappings)
    
    circular_fixes = fix_circular_references()

    working, total = verify_final_state()
    
    total_fixes = config_fixes + circular_fixes
    print(f"\\n🎉 {{LEAGUE_DISPLAY_NAME.upper()}} FIX COMPLETE!")
    print(f"Applied {{total_fixes}} canonical mapping fixes")
    if circular_fixes > 0:
        print(f"🚨 Fixed {{circular_fixes}} critical circular references!")
    print(f"Final success rate: {{(working/total)*100:.1f}}% ({{working}}/{{total}} teams)")
    
    if total_fixes > 0:
        print(f"🎯 Applied {{total_fixes}} fixes!")
    
    if working == total:
        print(f"🎯 Perfect! All {{LEAGUE_DISPLAY_NAME}} teams now have stats!")
    elif (working/total)*100 >= 90:
        print(f"🎯 Success! Achieved 90%+ target success rate!")
    else:
        remaining = total - working
        print(f"⚠️  {{remaining}} teams still need attention")

if __name__ == "__main__":
    main()
'''
    
    with open(script_name, 'w') as f:
        f.write(script_content)
    
    return script_name

def main():
    """Main function to run high priority league fixes."""
    print("🎯 RUNNING HIGH PRIORITY LEAGUE FIXES")
    print("=" * 70)
    print("Targeting leagues with 80-89% success rate for quick wins")
    
    # High priority leagues (80-89% success rate) - best candidates for reaching 90%
    high_priority_leagues = [
        ("AUSTRIA_2_LIGA", "Austria 2. Liga"),
        ("BAHRAIN_PREMIER_LEAGUE", "Bahrain Premier League"),
        ("CROATIA_PRVA_NL", "Croatia Prva NL"),
        ("KUWAIT_PREMIER_LEAGUE", "Kuwait Premier League"),
        ("SPAIN_SEGUNDA_DIVISION", "Spain Segunda Division"),
        ("SCOTLAND_CHAMPIONSHIP", "Scotland Championship"),
        ("ENGLAND_PREMIER_LEAGUE", "England Premier League"),
        ("ITALY_SERIE_D_GROUP_F", "Italy Serie D Group F"),
        ("SWITZERLAND_CHALLENGE_LEAGUE", "Switzerland Challenge League"),
        ("SPAIN_LA_LIGA", "Spain La Liga"),
        ("SAUDI_ARABIA_DIVISION_1", "Saudi Arabia Division 1"),
        ("ENGLAND_ISTHMIAN_LEAGUE", "England Isthmian League"),
        ("BRAZIL_CATARINENSE", "Brazil Catarinense"),
        ("MALTA_PREMIER_LEAGUE", "Malta Premier League"),
        ("CZECH_REPUBLIC_FIRST_LEAGUE", "Czech Republic First League"),
        ("IRELAND_PREMIER_DIVISION", "Ireland Premier Division"),
        ("POLAND_EKSTRAKLASA", "Poland Ekstraklasa"),
        ("INDIA_SUPER_LEAGUE", "India Super League"),
        ("AUSTRIA_BUNDESLIGA", "Austria Bundesliga"),
        ("SCOTLAND_PREMIERSHIP", "Scotland Premiership"),
    ]
    
    results = []
    successful_fixes = 0
    leagues_reaching_90 = 0
    
    print(f"\n📊 INITIAL STATUS CHECK")
    print("=" * 50)
    
    # Check initial status and filter leagues that need fixing
    leagues_to_fix = []
    for league_name, league_display_name in high_priority_leagues:
        working, total, success_rate = get_current_league_status(league_name)
        print(f"{league_name}: {working}/{total} ({success_rate:.1f}%)")
        
        if success_rate < 90 and total > 0:  # Only fix leagues under 90%
            leagues_to_fix.append((league_name, league_display_name))
    
    print(f"\n🔧 RUNNING FIXES FOR {len(leagues_to_fix)} LEAGUES")
    print("=" * 50)
    
    # Run fixes for each league
    for league_name, league_display_name in leagues_to_fix:
        print(f"\n{'='*70}")
        print(f"🎯 FIXING {league_name}")
        print(f"{'='*70}")
        
        # Get before status
        before_working, before_total, before_rate = get_current_league_status(league_name)
        print(f"Before: {before_working}/{before_total} ({before_rate:.1f}%)")
        
        # Check if we have a specific fix script, otherwise create a generic one
        script_name = f"fix_{league_name}.py"
        if not os.path.exists(script_name):
            print(f"Creating generic fix script for {league_name}...")
            script_name = create_generic_fix_script(league_name, league_display_name)
        
        # Run the fix
        success = run_fix_script(script_name)
        
        # Get after status
        after_working, after_total, after_rate = get_current_league_status(league_name)
        print(f"After:  {after_working}/{after_total} ({after_rate:.1f}%)")
        
        improvement = after_rate - before_rate
        improvement_str = f"(+{improvement:.1f}%)" if improvement > 0 else f"({improvement:.1f}%)" if improvement < 0 else "(no change)"
        
        if after_rate >= 90:
            leagues_reaching_90 += 1
            print(f"🎯 SUCCESS! {league_name} reached 90%+ target!")
        
        results.append({
            'league': league_name,
            'display_name': league_display_name,
            'script': script_name,
            'success': success,
            'before_rate': before_rate,
            'after_rate': after_rate,
            'improvement': improvement,
            'working': after_working,
            'total': after_total,
            'reached_90': after_rate >= 90
        })
        
        if success:
            successful_fixes += 1
            print(f"✅ {league_name} fix completed {improvement_str}")
        else:
            print(f"❌ {league_name} fix failed")
    
    # Final summary
    print(f"\n{'='*70}")
    print(f"📊 HIGH PRIORITY FIXES SUMMARY")
    print(f"{'='*70}")
    print(f"Leagues processed: {len(leagues_to_fix)}")
    print(f"Successful fixes: {successful_fixes}")
    print(f"Failed fixes: {len(leagues_to_fix) - successful_fixes}")
    print(f"Leagues reaching 90%+: {leagues_reaching_90}")
    
    print(f"\n📈 DETAILED RESULTS:")
    print("-" * 70)
    
    total_improvement = 0
    leagues_improved = 0
    
    for result in results:
        status = "SUCCESS" if result['success'] else "FAILED"
        improvement = result['improvement']
        improvement_str = f"(+{improvement:.1f}%)" if improvement > 0 else f"({improvement:.1f}%)" if improvement < 0 else "(no change)"
        
        if result['reached_90']:
            status += " 🎯 90%+"
        elif improvement > 5:
            status += " 📈 BIG+"
        elif improvement > 0:
            status += " ⬆️"
            leagues_improved += 1
        
        print(f"{status} {result['league']}: {result['working']}/{result['total']} "
              f"({result['after_rate']:.1f}%) {improvement_str}")
        
        total_improvement += improvement
    
    avg_improvement = total_improvement / len(results) if results else 0
    
    print(f"\n🎉 OVERALL IMPACT:")
    print(f"   Average improvement: {avg_improvement:.1f}%")
    print(f"   Leagues improved: {leagues_improved}")
    print(f"   Leagues reaching 90%+: {leagues_reaching_90}")
    print(f"   Total teams now working: {sum(r['working'] for r in results)}")
    print(f"   Total teams: {sum(r['total'] for r in results)}")
    
    if leagues_reaching_90 > 0:
        print(f"\n🎯 LEAGUES REACHING 90%+ TARGET:")
        for result in results:
            if result['reached_90']:
                print(f"   ✨ {result['display_name']}: {result['after_rate']:.1f}%")
    
    print(f"\n🚀 NEXT STEPS:")
    remaining_under_90 = len([r for r in results if r['after_rate'] < 90])
    if remaining_under_90 > 0:
        print(f"   • {remaining_under_90} high-priority leagues still under 90%")
        print(f"   • Consider creating more specific fix scripts for these leagues")
        print(f"   • Focus on medium-priority leagues (70-89%) next")
    else:
        print(f"   • All high-priority leagues now at 90%+!")
        print(f"   • Move to medium-priority leagues (70-89%)")

if __name__ == "__main__":
    main()