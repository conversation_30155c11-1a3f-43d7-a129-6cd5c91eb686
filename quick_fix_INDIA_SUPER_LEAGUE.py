#!/usr/bin/env python3
"""
Quick Fix for India Super League
Using the same approach that worked for AUSTRIA_2_LIGA
"""

import sys
sys.path.append('src')
from database.football_db import get_database

LEAGUE_NAME = "INDIA_SUPER_LEAGUE"

# Specific mappings based on analysis
SPECIFIC_MAPPINGS = [
    ("ATK Mohun Bagan FC", "ATK Mohun Bagan", "Remove FC suffix"),
    ("Minerva Punjab FC", "Minerva Punjab", "Remove FC suffix"),
    ("NorthEast United FC", "NorthEast United", "Remove FC suffix"),
]

def main():
    print(f"🎯 QUICK FIX FOR {LEAGUE_NAME}")
    print("=" * 50)
    
    fixes_applied = 0
    
    with get_database() as db:
        for old_team, target_team, reason in SPECIFIC_MAPPINGS:
            print(f"\n🔧 Fixing: {old_team} → {target_team}")
            print(f"   Reason: {reason}")
            
            # Get team IDs using execute_scalar
            old_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (old_team, LEAGUE_NAME))
            
            target_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (target_team, LEAGUE_NAME))
            
            if not old_id:
                print(f"   ❌ Old team '{old_team}' not found")
                continue
            
            if not target_id:
                print(f"   ❌ Target team '{target_team}' not found")
                continue
            
            # Verify target has stats
            target_stats = db.get_team_stats(target_team, LEAGUE_NAME)
            if target_stats.empty:
                print(f"   ⚠️  Target team '{target_team}' has no stats - skipping")
                continue
            
            try:
                # Apply mapping using direct connection
                db.conn.execute(
                    'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                    (target_id, old_id)
                )
                print(f"   ✅ Applied mapping")
                fixes_applied += 1
            except Exception as e:
                print(f"   ❌ Error applying mapping: {e}")
    
    print(f"\n💾 Committed {fixes_applied} fixes")
    
    # Check final status
    with get_database() as db:
        teams = db.execute_query('''
            SELECT t.team_name FROM teams t
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
        ''', (LEAGUE_NAME,))
        
        working = 0
        for _, team in teams.iterrows():
            stats = db.get_team_stats(team['team_name'], LEAGUE_NAME)
            if not stats.empty:
                working += 1
        
        success_rate = (working / len(teams)) * 100
        print(f"\n📊 Final result: {working}/{len(teams)} ({success_rate:.1f}%)")
        
        if success_rate >= 90:
            print(f"🎉 {LEAGUE_NAME} now above 90%!")
        else:
            print(f"⚠️  Still needs {90 - success_rate:.1f}% improvement")

if __name__ == "__main__":
    main()