/* Custom CSS for Football Database Web Interface */

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Statistics Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-danger,
.card.bg-info {
    border: none;
}

.card.bg-primary .card-body,
.card.bg-success .card-body,
.card.bg-warning .card-body,
.card.bg-danger .card-body,
.card.bg-info .card-body {
    padding: 1.5rem;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Badges */
.badge {
    font-size: 0.875em;
}

/* Buttons */
.btn {
    font-weight: 500;
    border-radius: 0.375rem;
}

.btn-outline-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 123, 255, 0.25);
}

/* Jumbotron */
.jumbotron {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 0.5rem;
}

/* League Cards */
.league-card {
    transition: transform 0.2s ease-in-out;
}

.league-card:hover {
    transform: translateY(-2px);
}

/* Team Statistics */
.team-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

/* Head-to-Head */
.h2h-comparison {
    background: linear-gradient(90deg, #007bff 0%, #6c757d 50%, #dc3545 100%);
    height: 4px;
    border-radius: 2px;
    margin: 1rem 0;
}

/* Charts */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* Search */
.search-highlight {
    background-color: #fff3cd;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
}

/* Responsive */
@media (max-width: 768px) {
    .jumbotron {
        padding: 2rem 1rem;
    }
    
    .jumbotron h1 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Form Enhancements */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Tab Enhancements */
.nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    background: none;
    color: #6c757d;
    font-weight: 500;
}

.nav-tabs .nav-link:hover {
    border-bottom-color: #dee2e6;
    color: #007bff;
}

.nav-tabs .nav-link.active {
    border-bottom-color: #007bff;
    color: #007bff;
    background: none;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(45deg, #007bff, #6610f2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Match Result Cards */
.match-card {
    border-left: 4px solid #007bff;
    transition: all 0.2s ease-in-out;
}

.match-card:hover {
    border-left-color: #0056b3;
    transform: translateX(2px);
}

/* Team Form Indicators */
.form-indicator {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    text-align: center;
    line-height: 20px;
    font-size: 0.75rem;
    font-weight: bold;
    margin: 0 2px;
}

.form-indicator.win {
    background-color: #28a745;
    color: white;
}

.form-indicator.draw {
    background-color: #ffc107;
    color: #212529;
}

.form-indicator.loss {
    background-color: #dc3545;
    color: white;
}

/* Progress Bars */
.progress {
    height: 0.5rem;
    border-radius: 0.25rem;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Sidebar Styles */
.sidebar-container {
    padding: 0;
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
    min-height: calc(100vh - 56px); /* Adjust for navbar height */
}

.sidebar {
    position: sticky;
    top: 0;
    height: calc(100vh - 56px);
    overflow-y: auto;
    padding: 1rem 0;
}

.sidebar-header {
    padding: 0 1rem 1rem 1rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 1rem;
}

.sidebar-header h6 {
    margin-bottom: 0.5rem;
    color: #495057;
    font-weight: 600;
}

.sidebar-search {
    margin-top: 0.5rem;
}

.sidebar-content {
    padding: 0 0.5rem;
}

.league-group {
    margin-bottom: 0.5rem;
}

.league-group-header {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    background-color: #e9ecef;
    border-radius: 0.25rem;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.875rem;
    color: #495057;
    transition: background-color 0.2s ease;
}

.league-group-header:hover {
    background-color: #dee2e6;
}

.league-group-header .fas {
    transition: transform 0.2s ease;
    font-size: 0.75rem;
}

.league-list {
    padding: 0.25rem 0;
}

.league-link {
    display: block;
    padding: 0.375rem 0.75rem;
    margin: 0.125rem 0;
    color: #6c757d;
    text-decoration: none;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.league-link:hover {
    background-color: #e9ecef;
    color: #495057;
    text-decoration: none;
    padding-left: 1rem;
}

.league-link.active {
    background-color: #007bff;
    color: white;
    border-left-color: #0056b3;
    font-weight: 500;
}

.league-link.active:hover {
    background-color: #0056b3;
    color: white;
}

.main-content {
    padding: 0 1rem;
}

/* Mobile sidebar toggle button */
#sidebarToggle {
    background: none;
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

#sidebarToggle:hover {
    background-color: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
}

/* Responsive adjustments for sidebar */
@media (max-width: 767.98px) {
    .sidebar-container {
        position: fixed;
        top: 56px; /* Navbar height */
        left: -100%;
        width: 280px;
        height: calc(100vh - 56px);
        z-index: 1040;
        transition: left 0.3s ease;
        background-color: white;
        box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    }
    
    .sidebar-container.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
        width: 100%;
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        z-index: 1039;
        display: none;
    }
    
    .sidebar-overlay.show {
        display: block;
    }
}

@media (min-width: 768px) {
    #sidebarToggle {
        display: none;
    }
}

/* Dual Sidebar Styles */
.league-sidebar-container,
.team-sidebar-container {
    padding: 0;
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
    min-height: calc(100vh - 56px);
}

.team-sidebar-container {
    background-color: #f1f3f4;
    border-left: 1px solid #dee2e6;
}

.main-content-dual {
    padding: 0 1rem;
}

/* Team sidebar specific styles */
.team-list {
    padding: 0.25rem 0;
}

.team-link {
    display: block;
    padding: 0.5rem 0.75rem;
    margin: 0.125rem 0;
    color: #6c757d;
    text-decoration: none;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.team-link:hover {
    background-color: #e9ecef;
    color: #495057;
    text-decoration: none;
    padding-left: 1rem;
}

.team-link.active {
    background-color: #28a745;
    color: white;
    border-left-color: #1e7e34;
    font-weight: 500;
}

.team-link.active:hover {
    background-color: #218838;
    color: white;
}

/* Enhanced team link styling */
.team-link-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.team-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.team-actions {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.team-link:hover .team-actions {
    opacity: 1;
}

.team-link.active .team-actions {
    opacity: 1;
}

/* Team sidebar sections */
.team-sidebar-section {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.sidebar-section-header {
    padding: 0 0.75rem 0.5rem 0.75rem;
}

.sidebar-section-header h6 {
    margin: 0;
    color: #495057;
    font-weight: 600;
    font-size: 0.8rem;
}

/* Quick H2H links */
.quick-h2h-list {
    max-height: 200px;
    overflow-y: auto;
}

.quick-h2h-link {
    display: block;
    padding: 0.375rem 0.75rem;
    margin: 0.125rem 0;
    color: #6c757d;
    text-decoration: none;
    font-size: 0.8rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.quick-h2h-link:hover {
    background-color: #e9ecef;
    color: #495057;
    text-decoration: none;
    padding-left: 1rem;
    border-left-color: #ffc107;
}

.quick-h2h-link.active {
    background-color: #ffc107;
    color: #212529;
    border-left-color: #e0a800;
    font-weight: 500;
}

.quick-h2h-link.active:hover {
    background-color: #e0a800;
    color: #212529;
}

/* H2H team sections */
.h2h-team-section {
    margin-bottom: 0.75rem;
}

.h2h-team-section:last-child {
    margin-bottom: 0;
}

.h2h-team-section small {
    display: block;
    padding: 0.25rem 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Mobile sidebar toggle buttons */
#leagueSidebarToggle,
#teamSidebarToggle {
    background: none;
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    margin-right: 0.25rem;
}

#leagueSidebarToggle:hover,
#teamSidebarToggle:hover {
    background-color: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
}

/* Responsive adjustments for dual sidebars */
@media (max-width: 991.98px) {
    .league-sidebar-container,
    .team-sidebar-container {
        position: fixed;
        top: 56px;
        width: 280px;
        height: calc(100vh - 56px);
        z-index: 1040;
        transition: left 0.3s ease;
        background-color: white;
        box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    }
    
    .league-sidebar-container {
        left: -100%;
    }
    
    .team-sidebar-container {
        left: -100%;
    }
    
    .league-sidebar-container.show {
        left: 0;
    }
    
    .team-sidebar-container.show {
        left: 0;
    }
    
    .main-content-dual {
        margin-left: 0;
        width: 100%;
    }
}

@media (min-width: 992px) {
    #leagueSidebarToggle,
    #teamSidebarToggle {
        display: none;
    }
}

/* Adjust main content width for dual sidebars on larger screens */
@media (min-width: 992px) {
    .main-content-dual {
        max-width: calc(100% - 2rem);
    }
}

/* H2H Compact Styling */
.h2h-summary-card {
    min-height: 80px;
}

.h2h-summary-card .card-body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.h2h-compact-card {
    height: 100%;
}

.h2h-compact-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.h2h-compact-card .table td {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
}

.h2h-compact-card .table td:first-child {
    width: 70%;
}

.h2h-compact-card .table td:last-child {
    width: 30%;
    font-weight: 600;
}