#!/usr/bin/env python3
"""
Batch League Fixer

Efficiently fixes multiple leagues in sequence, targeting leagues with
good success rates and available config mappings.
"""

import sys
import importlib.util
from typing import Dict, List, Tuple

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

# ============================================================================
# LEAGUES TO FIX (in priority order)
# ============================================================================

LEAGUES_TO_FIX = [
    "ANDORRA_PRIMERA_DIVISIO",
    "AUSTRALIA_A_LEAGUE_WOMEN", 
    "AUSTRALIA_CAPITAL_TERRITORY_NPL",
    "AUSTRALIA_NEW_SOUTH_WALES_NPL",
    "AUSTRIA_BUNDESLIGA",
    "BELARUS_VYSHEYSHAYA_LIGA",
    "BRAZIL_SERIE_A", 
    "BULGARIA_FIRST_LEAGUE",
    "CHINA_SUPER_LEAGUE",
    "CROATIA_1_HNL",
    "CZECH_REPUBLIC_FIRST_LEAGUE",
    "DENMARK_SUPERLIGA",
    "ENGLAND_PREMIER_LEAGUE",
    "FINLAND_VEIKKAUSLIIGA",
    "FRANCE_LIGUE_1",
    "GERMANY_BUNDESLIGA",
    "GREECE_SUPER_LEAGUE",
    "ISRAEL_PREMIER_LEAGUE",
    "ITALY_SERIE_A",
    "NETHERLANDS_EREDIVISIE"
]

def fix_league(league_name: str) -> Tuple[int, int, float]:
    """Fix a single league and return (working_teams, total_teams, success_rate)."""
    
    print(f"\n{'='*70}")
    print(f"🏆 FIXING {league_name}")
    print(f"{'='*70}")
    
    with get_database() as db:
        # Analyze current state
        teams = db.execute_query('''
            SELECT t.team_name, t.team_id, t.canonical_team_id, 
                   t2.team_name as canonical_name
            FROM teams t
            LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY t.team_name
        ''', (league_name,))
        
        if teams.empty:
            print(f"❌ No teams found for {league_name}")
            return 0, 0, 0.0
        
        total_teams = len(teams)
        working_teams_before = 0
        
        # Count initial working teams
        for _, team in teams.iterrows():
            stats = db.get_team_stats(team['team_name'], league_name)
            if not stats.empty:
                working_teams_before += 1
        
        initial_rate = (working_teams_before / total_teams) * 100
        print(f"📊 Initial: {working_teams_before}/{total_teams} teams ({initial_rate:.1f}%)")
        
        # Skip if already >95%
        if initial_rate > 95:
            print(f"✅ Already >95% - skipping")
            return working_teams_before, total_teams, initial_rate
        
        fixes_applied = 0
        
        # STEP 1: Fix circular references (teams with stats pointing to teams without stats)
        problematic_teams = db.execute_query('''
            SELECT t.team_id, t.team_name, t.canonical_team_id,
                   COUNT(ts.team_id) as stat_count
            FROM teams t
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ? AND t.team_id != t.canonical_team_id
            GROUP BY t.team_id, t.team_name, t.canonical_team_id
            HAVING stat_count > 0
        ''', (league_name,))
        
        if not problematic_teams.empty:
            print(f"🚨 Fixing {len(problematic_teams)} circular references...")
            for _, team in problematic_teams.iterrows():
                db.conn.execute(
                    'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                    (team['team_id'], team['team_id'])
                )
                fixes_applied += 1
        
        # STEP 2: Apply common pattern fixes
        common_patterns = [
            # Remove common prefixes/suffixes
            ("FC ", ""),
            ("KF ", ""),
            ("CF ", ""), 
            ("AC ", ""),
            ("SC ", ""),
            ("RC ", ""),
            ("SK ", ""),
            ("NK ", ""),
            ("FK ", ""),
            ("VfB ", ""),
            ("VfL ", ""),
            ("TSV ", ""),
            ("SV ", ""),
            ("1. FC ", ""),
            ("1. FSV ", ""),
            (" FC", ""),
            (" AC", ""),
            (" SC", ""),
            (" United", ""),
            (" City", ""),
            (" Town", ""),
        ]
        
        # Find teams without stats and try to map them using pattern matching
        teams_without_stats = []
        teams_with_stats = {}
        
        for _, team in teams.iterrows():
            team_name = team['team_name']
            stats = db.get_team_stats(team_name, league_name)
            if stats.empty:
                teams_without_stats.append(team_name)
            else:
                teams_with_stats[team_name] = team['team_id']
        
        # Try to match teams without stats to teams with stats using patterns
        for team_without_stats in teams_without_stats:
            best_match = None
            
            # Try removing/adding common patterns
            for prefix, replacement in common_patterns:
                if team_without_stats.startswith(prefix):
                    candidate = team_without_stats[len(prefix):]
                elif team_without_stats.endswith(prefix[:-1]):  # Remove space for suffix patterns
                    candidate = team_without_stats[:-len(prefix)+1] 
                else:
                    candidate = prefix + team_without_stats
                
                if candidate in teams_with_stats:
                    best_match = candidate
                    break
            
            # If we found a match, apply the mapping
            if best_match:
                old_id = db.execute_scalar('''
                    SELECT t.team_id FROM teams t
                    JOIN leagues l ON t.league_id = l.league_id
                    WHERE t.team_name = ? AND l.league_name = ?
                ''', (team_without_stats, league_name))
                
                target_id = teams_with_stats[best_match]
                
                if old_id and target_id:
                    try:
                        db.conn.execute(
                            'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                            (target_id, old_id)
                        )
                        print(f"   ✅ {team_without_stats} → {best_match}")
                        fixes_applied += 1
                    except Exception:
                        pass  # Skip on error
        
        # Commit all fixes
        if fixes_applied > 0:
            db.conn.commit()
        
        # Count final working teams
        working_teams_after = 0
        for _, team in teams.iterrows():
            stats = db.get_team_stats(team['team_name'], league_name)
            if not stats.empty:
                working_teams_after += 1
        
        final_rate = (working_teams_after / total_teams) * 100
        improvement = working_teams_after - working_teams_before
        
        print(f"📈 Final: {working_teams_after}/{total_teams} teams ({final_rate:.1f}%)")
        print(f"🎯 Improved by +{improvement} teams ({improvement/total_teams*100:.1f} percentage points)")
        print(f"🔧 Applied {fixes_applied} fixes")
        
        success_status = "🎯 SUCCESS!" if final_rate >= 90 else "⚠️  Needs more work" if final_rate >= 70 else "❌ Poor results"
        print(f"{success_status}")
        
        return working_teams_after, total_teams, final_rate

def main():
    """Main function - process multiple leagues."""
    print("🚀 BATCH LEAGUE FIXER")
    print("=" * 70)
    print("Systematically fixing multiple leagues to achieve >90% success rates")
    
    results = []
    leagues_fixed = 0
    target_leagues = 20
    
    for league_name in LEAGUES_TO_FIX:
        if leagues_fixed >= target_leagues:
            break
            
        working, total, success_rate = fix_league(league_name)
        
        if total > 0:  # Only count leagues that exist
            results.append({
                'league': league_name,
                'working': working,
                'total': total,
                'success_rate': success_rate
            })
            leagues_fixed += 1
    
    # Summary report
    print(f"\n{'='*70}")
    print(f"📊 BATCH FIXING COMPLETE - PROCESSED {leagues_fixed} LEAGUES")
    print(f"{'='*70}")
    
    successful_leagues = 0
    total_teams_fixed = 0
    
    for result in results:
        status = "🎯" if result['success_rate'] >= 90 else "⚠️" if result['success_rate'] >= 70 else "❌"
        print(f"{status} {result['league']}: {result['working']}/{result['total']} ({result['success_rate']:.1f}%)")
        
        if result['success_rate'] >= 90:
            successful_leagues += 1
        
        total_teams_fixed += result['working']
    
    print(f"\n📈 SUMMARY:")
    print(f"   Leagues with >90% success rate: {successful_leagues}/{leagues_fixed}")
    print(f"   Total teams now working: {total_teams_fixed}")
    print(f"   Average success rate: {sum(r['success_rate'] for r in results)/len(results):.1f}%")
    
    if successful_leagues >= 10:
        print(f"🎉 EXCELLENT! {successful_leagues} leagues now have >90% success rates!")
    elif successful_leagues >= 5:
        print(f"✅ Good progress! {successful_leagues} leagues now have >90% success rates!")
    else:
        print(f"⚠️  Only {successful_leagues} leagues achieved >90% - may need manual analysis")

if __name__ == "__main__":
    main()
