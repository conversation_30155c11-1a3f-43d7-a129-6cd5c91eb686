#!/usr/bin/env python3
"""
Fix Team Mappings Using League Config Files

This script uses the TEAM_NAME_MAPPING dictionaries from league config files
to fix canonical team mappings with real-world knowledge about team name changes,
mergers, and rebrands.
"""

import sys
import os
import importlib.util
from pathlib import Path
from typing import Dict, List, Tuple

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

def load_league_config(config_path: str) -> Dict:
    """Load a league config file and extract the team name mapping."""
    try:
        spec = importlib.util.spec_from_file_location("config", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        
        # Get the current config
        current_config = getattr(config_module, 'CURRENT_CONFIG', None)
        if current_config and 'TEAM_NAME_MAPPING' in current_config:
            return current_config['TEAM_NAME_MAPPING']
        
        # Fallback: try to get TEAM_NAME_MAPPING directly
        team_mapping = getattr(config_module, 'TEAM_NAME_MAPPING', None)
        if team_mapping:
            return team_mapping
            
        return {}
    except Exception as e:
        print(f"   ❌ Error loading {config_path}: {e}")
        return {}

def get_all_league_mappings() -> Dict[str, Dict[str, str]]:
    """Get team name mappings from all league config files."""
    print("🔍 LOADING TEAM MAPPINGS FROM CONFIG FILES")
    print("=" * 60)
    
    config_dir = Path("src/scrapers/league_configs")
    all_mappings = {}
    
    if not config_dir.exists():
        print(f"❌ Config directory not found: {config_dir}")
        return {}
    
    config_files = list(config_dir.glob("*.py"))
    print(f"Found {len(config_files)} config files")
    
    for config_file in config_files:
        if config_file.name == "__init__.py":
            continue
            
        league_name = config_file.stem
        print(f"\n📋 Processing {league_name}...")
        
        mappings = load_league_config(str(config_file))
        if mappings:
            all_mappings[league_name] = mappings
            print(f"   ✅ Found {len(mappings)} team mappings")
            for current_name, old_name in mappings.items():
                print(f"      {current_name} <- {old_name}")
        else:
            print(f"   ⚠️  No team mappings found")
    
    print(f"\n📊 Total leagues with mappings: {len(all_mappings)}")
    return all_mappings

def apply_config_mappings(all_mappings: Dict[str, Dict[str, str]]):
    """Apply the config-based team mappings to the database."""
    print("\n🔧 APPLYING CONFIG-BASED TEAM MAPPINGS")
    print("=" * 60)
    
    total_fixes = 0
    
    with get_database() as db:
        for league_name, mappings in all_mappings.items():
            print(f"\n🏆 Processing {league_name}")
            print("-" * 40)
            
            league_fixes = 0
            
            for current_team, old_team in mappings.items():
                try:
                    # Find the current team (should be canonical)
                    current_team_query = db.execute_query('''
                        SELECT t.team_id, t.canonical_team_id
                        FROM teams t
                        JOIN leagues l ON t.league_id = l.league_id
                        WHERE t.team_name = ? AND l.league_name = ?
                    ''', (current_team, league_name))
                    
                    # Find the old team (should point to current team)
                    old_team_query = db.execute_query('''
                        SELECT t.team_id, t.canonical_team_id
                        FROM teams t
                        JOIN leagues l ON t.league_id = l.league_id
                        WHERE t.team_name = ? AND l.league_name = ?
                    ''', (old_team, league_name))
                    
                    if current_team_query.empty:
                        print(f"   ⚠️  Current team '{current_team}' not found")
                        continue
                    
                    if old_team_query.empty:
                        print(f"   ⚠️  Old team '{old_team}' not found")
                        continue
                    
                    current_team_id = current_team_query.iloc[0]['team_id']
                    old_team_id = old_team_query.iloc[0]['team_id']
                    old_canonical_id = old_team_query.iloc[0]['canonical_team_id']
                    
                    # Check if the old team already points to the current team
                    if old_canonical_id == current_team_id:
                        print(f"   ✅ {old_team} -> {current_team} (already correct)")
                        continue
                    
                    # Update the old team to point to the current team
                    db.conn.execute(
                        "UPDATE teams SET canonical_team_id = ? WHERE team_id = ?",
                        (current_team_id, old_team_id)
                    )
                    
                    print(f"   ✅ Updated {old_team} -> {current_team}")
                    league_fixes += 1
                    total_fixes += 1
                    
                except Exception as e:
                    print(f"   ❌ Error processing {current_team} <- {old_team}: {e}")
            
            if league_fixes > 0:
                db.conn.commit()
                print(f"   💾 Committed {league_fixes} fixes for {league_name}")
            else:
                print(f"   ✅ No fixes needed for {league_name}")
    
    print(f"\n🎉 TOTAL CONFIG-BASED FIXES APPLIED: {total_fixes}")
    return total_fixes

def test_armenia_fix():
    """Test the specific Armenia Premier League fix."""
    print("\n🧪 TESTING ARMENIA PREMIER LEAGUE FIX")
    print("=" * 50)
    
    with get_database() as db:
        # Test Artsakh -> Noah mapping
        print("Testing Artsakh -> Noah mapping:")
        
        # Check if Artsakh exists and what it points to
        artsakh_check = db.execute_query('''
            SELECT t1.team_name, t1.team_id, t1.canonical_team_id, t2.team_name as canonical_name
            FROM teams t1
            LEFT JOIN teams t2 ON t1.canonical_team_id = t2.team_id
            JOIN leagues l ON t1.league_id = l.league_id
            WHERE l.league_name = "ARMENIA_PREMIER_LEAGUE"
            AND (t1.team_name LIKE "%Artsakh%" OR t1.team_name = "Noah")
        ''')
        
        if not artsakh_check.empty:
            for _, row in artsakh_check.iterrows():
                print(f"   {row['team_name']} (id: {row['team_id']}) -> {row['canonical_name']} (canonical_id: {row['canonical_team_id']})")
        
        # Test team stats for Noah
        noah_stats = db.get_team_stats('Noah', 'ARMENIA_PREMIER_LEAGUE')
        if not noah_stats.empty:
            print(f"   ✅ Noah has team stats: {len(noah_stats)} records")
        else:
            print(f"   ❌ Noah has no team stats")
        
        # Test team stats for Artsakh (should now work via canonical mapping)
        artsakh_stats = db.get_team_stats('Artsakh', 'ARMENIA_PREMIER_LEAGUE')
        if not artsakh_stats.empty:
            print(f"   ✅ Artsakh now has team stats via canonical mapping: {len(artsakh_stats)} records")
        else:
            print(f"   ❌ Artsakh still has no team stats")

def verify_other_leagues():
    """Verify fixes in other leagues with config mappings."""
    print("\n🔍 VERIFYING OTHER LEAGUE FIXES")
    print("=" * 50)
    
    # Test a few other leagues that should have been fixed
    test_cases = [
        ("SPAIN_LA_LIGA", "Alaves", "Deportivo Alavés"),
        ("ENGLAND_PREMIER_LEAGUE", "Brighton", "Brighton & Hove Albion"),
        ("AUSTRALIA_QUEENSLAND_NPL", "Queensland Lions", "Queensland Lions FC"),
    ]
    
    with get_database() as db:
        for league, canonical_team, old_team in test_cases:
            try:
                # Check if the mapping exists
                mapping_check = db.execute_query('''
                    SELECT t1.team_name as old_name, t2.team_name as canonical_name
                    FROM teams t1
                    JOIN teams t2 ON t1.canonical_team_id = t2.team_id
                    JOIN leagues l ON t1.league_id = l.league_id
                    WHERE t1.team_name = ? AND l.league_name = ?
                ''', (old_team, league))
                
                if not mapping_check.empty:
                    canonical_name = mapping_check.iloc[0]['canonical_name']
                    if canonical_name == canonical_team:
                        print(f"   ✅ {league}: {old_team} -> {canonical_name}")
                    else:
                        print(f"   ⚠️  {league}: {old_team} -> {canonical_name} (expected: {canonical_team})")
                else:
                    print(f"   ❌ {league}: {old_team} not found")
                    
            except Exception as e:
                print(f"   ❌ {league}: Error checking {old_team}: {e}")

def main():
    """Main function."""
    print("🔧 TEAM MAPPING FIX USING CONFIG FILES")
    print("=" * 60)
    print("Using real-world team name mappings from league config files")
    print("to fix canonical team mappings with accurate knowledge.")
    
    # Load all config mappings
    all_mappings = get_all_league_mappings()
    
    if not all_mappings:
        print("❌ No team mappings found in config files")
        return
    
    # Apply the mappings
    total_fixes = apply_config_mappings(all_mappings)
    
    # Test specific cases
    test_armenia_fix()
    verify_other_leagues()
    
    print(f"\n🎉 CONFIG-BASED MAPPING FIX COMPLETE!")
    print(f"Applied {total_fixes} fixes using real-world team knowledge.")
    print("Teams like Artsakh should now have proper canonical mappings!")

if __name__ == "__main__":
    main()
