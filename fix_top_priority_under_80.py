#!/usr/bin/env python3
"""
Fix Top Priority Leagues Under 80%

Targeting the 5 highest leagues in 70-79% range for quick wins.
"""

import sys
sys.path.append('src')
from database.football_db import get_database

# Define mappings for each league based on analysis
LEAGUE_MAPPINGS = {
    'PANAMA_LIGA_PANAMENA_DE_FUTBOL': [
        ("CA Independiente de La Chorrera", "Independiente", "Use shorter name"),
        ("CD Árabe Unido", "Árabe Unido", "Remove CD prefix"),
        ("Costa Del Este", "Costa del Este FC", "Use FC version"),
        ("UMECIT FC", "Umecit", "Remove FC suffix"),
    ],
    
    'HONDURAS_LIGA_NACIONAL': [
        ("CD Marathón", "Marathón", "Remove CD prefix"),
        ("Club Deportivo Génesis", "Génesis", "Use shorter name"),
        ("Lobos UPNFM", "Upnfm", "Use shorter name"),
        ("Real CD España", "Real España", "Remove CD"),
    ],
    
    'IRELAND_FIRST_DIVISION': [
        ("Dundalk", "Dundalk FC", "Use FC version"),
        ("Treaty United FC", "Treaty United", "Remove FC suffix"),
        ("UCD", "University College Dublin FC", "Use full name"),
    ],
    
    'MALAYSIA_SUPER_LEAGUE': [
        ("Johor Darul Ta'zim FC", "Johor Darul Ta'zim", "Remove FC suffix"),
        ("Pdrm", "Polis Di-Raja Malaysia FA", "Use full name"),
        ("Penang FA", "Penang", "Use shorter name"),
        ("Persatuan Bolasepak Selangor", "Selangor", "Use shorter name"),
    ],
    
    'PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_C': [
        ("Arronches e Benfica", "Sport Arronches e Benfica", "Use Sport prefix"),
        ("CD Fátima", "Fátima", "Remove CD prefix"),
        ("CF União de Coimbra", "União de Coimbra", "Remove CF prefix"),
        ("FC Alverca II", "Alverca", "Use main team name"),
        ("Mortágua FC", "Mortágua", "Remove FC suffix"),
        ("Sport Benfica e Castelo Branco", "Benfica Castelo Branco", "Use shorter name"),
    ]
}

def apply_mapping(old_team: str, target_team: str, league_name: str, reason: str) -> bool:
    """Apply a canonical mapping between two teams."""
    with get_database() as db:
        try:
            # Get team IDs using execute_scalar
            old_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (old_team, league_name))
            
            target_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (target_team, league_name))
            
            if not old_id:
                print(f"   ❌ Old team '{old_team}' not found")
                return False
            
            if not target_id:
                print(f"   ❌ Target team '{target_team}' not found")
                return False
            
            # Verify target has stats
            target_stats = db.get_team_stats(target_team, league_name)
            if target_stats.empty:
                print(f"   ⚠️  Target team '{target_team}' has no stats - skipping")
                return False
            
            # Apply mapping using direct connection
            db.conn.execute(
                'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                (target_id, old_id)
            )
            print(f"   ✅ Applied mapping")
            return True
            
        except Exception as e:
            print(f"   ❌ Error applying mapping: {e}")
            return False

def fix_league(league_name: str) -> tuple:
    """Fix a single league and return before/after rates."""
    print(f"\n{'='*70}")
    print(f"🎯 FIXING {league_name}")
    print(f"{'='*70}")
    
    # Get initial status
    with get_database() as db:
        teams = db.execute_query('''
            SELECT t.team_name FROM teams t
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
        ''', (league_name,))
        
        working_before = 0
        for _, team in teams.iterrows():
            stats = db.get_team_stats(team['team_name'], league_name)
            if not stats.empty:
                working_before += 1
        
        before_rate = (working_before / len(teams)) * 100
        print(f"📊 Before: {working_before}/{len(teams)} ({before_rate:.1f}%)")
    
    # Apply mappings
    mappings = LEAGUE_MAPPINGS.get(league_name, [])
    if not mappings:
        print(f"⚠️  No mappings defined for {league_name}")
        return before_rate, before_rate
    
    print(f"🔧 Applying {len(mappings)} mappings...")
    fixes_applied = 0
    
    for old_team, target_team, reason in mappings:
        print(f"\n🔧 Fixing: {old_team} → {target_team}")
        print(f"   Reason: {reason}")
        
        if apply_mapping(old_team, target_team, league_name, reason):
            fixes_applied += 1
    
    # Get final status
    with get_database() as db:
        working_after = 0
        for _, team in teams.iterrows():
            stats = db.get_team_stats(team['team_name'], league_name)
            if not stats.empty:
                working_after += 1
        
        after_rate = (working_after / len(teams)) * 100
        print(f"\n📊 After: {working_after}/{len(teams)} ({after_rate:.1f}%)")
        print(f"📈 Improvement: {after_rate - before_rate:.1f}%")
        print(f"💾 Fixes applied: {fixes_applied}")
        
        if after_rate >= 90:
            print(f"🎉 {league_name} now above 90%!")
        elif after_rate >= 80:
            print(f"🎯 {league_name} now above 80%!")
        
        return before_rate, after_rate

def main():
    """Main function to fix top priority leagues under 80%."""
    print("🎯 FIXING TOP PRIORITY LEAGUES UNDER 80%")
    print("=" * 70)
    print("Targeting 5 highest leagues in 70-79% range")
    
    target_leagues = [
        'PANAMA_LIGA_PANAMENA_DE_FUTBOL',
        'HONDURAS_LIGA_NACIONAL', 
        'IRELAND_FIRST_DIVISION',
        'MALAYSIA_SUPER_LEAGUE',
        'PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_C'
    ]
    
    results = []
    total_improvement = 0
    leagues_reaching_80 = 0
    leagues_reaching_90 = 0
    
    for league_name in target_leagues:
        before_rate, after_rate = fix_league(league_name)
        improvement = after_rate - before_rate
        total_improvement += improvement
        
        if after_rate >= 90:
            leagues_reaching_90 += 1
        elif after_rate >= 80:
            leagues_reaching_80 += 1
        
        results.append({
            'league': league_name,
            'before': before_rate,
            'after': after_rate,
            'improvement': improvement
        })
    
    # Final summary
    print(f"\n{'='*70}")
    print(f"📊 SUMMARY: TOP PRIORITY FIXES")
    print(f"{'='*70}")
    print(f"Leagues processed: {len(target_leagues)}")
    print(f"Leagues reaching 90%+: {leagues_reaching_90}")
    print(f"Leagues reaching 80%+: {leagues_reaching_80}")
    print(f"Average improvement: {total_improvement / len(target_leagues):.1f}%")
    
    print(f"\n📈 DETAILED RESULTS:")
    print("-" * 70)
    
    for result in results:
        improvement_str = f"(+{result['improvement']:.1f}%)" if result['improvement'] > 0 else f"({result['improvement']:.1f}%)" if result['improvement'] < 0 else "(no change)"
        
        if result['after'] >= 90:
            status = "🎉 EXCELLENT"
        elif result['after'] >= 80:
            status = "🎯 GOOD"
        else:
            status = "⚠️  NEEDS MORE"
        
        print(f"{status} {result['league']}: {result['after']:.1f}% {improvement_str}")

if __name__ == "__main__":
    main()