#!/usr/bin/env python3
"""
Batch Fix Multiple Leagues

This script fixes multiple leagues at once using the proven methodology.
It applies config mappings, fixes circular references, and uses smart pattern matching.
"""

import sys
import importlib.util
import os
from typing import Dict, List, Tuple, Set
from collections import defaultdict

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

# ============================================================================
# CONFIGURATION
# ============================================================================

# Top priority leagues to fix (from our analysis)
PRIORITY_LEAGUES = [
    "ALBANIA_SUPERLIGA",  # Already fixed - test case
    "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_C",
    "MALTA_FIRST_DIVISION", 
    "GERMANY_OBERLIGA_NIEDERRHEIN",
    "IRAN_PERSIAN_GULF_PRO_LEAGUE",
    "ENGLAND_PROFESSIONAL_DEVELOPMENT_LEAGUE",
    "SCOTLAND_HIGHLAND_FOOTBALL_LEAGUE",
    "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_A",
    "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_B",
    "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_D"
]

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

def load_league_config(league_name: str) -> Dict[str, str]:
    """Load config mappings for a league."""
    config_path = f"src/scrapers/league_configs/{league_name}.py"
    
    try:
        spec = importlib.util.spec_from_file_location("config", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        
        # Get the current config
        current_config = getattr(config_module, 'CURRENT_CONFIG', None)
        if current_config and 'TEAM_NAME_MAPPING' in current_config:
            return current_config['TEAM_NAME_MAPPING']
        
        # Fallback: try to get TEAM_NAME_MAPPING directly
        team_mapping = getattr(config_module, 'TEAM_NAME_MAPPING', None)
        if team_mapping:
            return team_mapping
            
        return {}
    except Exception:
        return {}

def analyze_league_state(league_name: str) -> Dict:
    """Analyze current state of a league."""
    with get_database() as db:
        # Get all teams in the league
        teams = db.execute_query('''
            SELECT t.team_name, t.team_id, t.canonical_team_id, 
                   t2.team_name as canonical_name
            FROM teams t
            LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY t.team_name
        ''', (league_name,))
        
        if teams.empty:
            return {'total_teams': 0, 'working_teams': 0, 'teams_info': {}}
        
        # Check which teams have stats
        teams_info = {}
        working_teams = 0
        
        for _, team in teams.iterrows():
            team_name = team['team_name']
            team_id = team['team_id']
            canonical_name = team['canonical_name']
            
            # Check if team has stats
            stats = db.get_team_stats(team_name, league_name)
            has_stats = not stats.empty
            
            if has_stats:
                working_teams += 1
            
            teams_info[team_name] = {
                'team_id': team_id,
                'canonical_name': canonical_name,
                'has_stats': has_stats,
                'canonical_id': team['canonical_team_id']
            }
        
        return {
            'total_teams': len(teams),
            'working_teams': working_teams,
            'teams_info': teams_info,
            'success_rate': (working_teams / len(teams)) * 100 if len(teams) > 0 else 0
        }

def find_smart_mappings(league_name: str, teams_info: Dict) -> List[Tuple[str, str, str]]:
    """Find smart mappings using pattern matching."""
    mappings = []
    
    # Get teams without stats and teams with stats
    teams_without_stats = [name for name, info in teams_info.items() if not info['has_stats']]
    teams_with_stats = [name for name, info in teams_info.items() if info['has_stats']]
    
    for team_without_stats in teams_without_stats:
        # Try common patterns
        base_name = team_without_stats
        
        # Remove common prefixes/suffixes
        for prefix in ['KF ', 'FC ', 'AF ', 'KS ', 'Kf ', 'Fc ', 'Af ', 'Ks ']:
            if base_name.startswith(prefix):
                base_name = base_name[len(prefix):]
                break
        
        for suffix in [' FC', ' KF', ' AF', ' KS']:
            if base_name.endswith(suffix):
                base_name = base_name[:-len(suffix)]
                break
        
        # Look for matches in teams with stats
        for team_with_stats in teams_with_stats:
            # Exact base name match
            if base_name.lower() == team_with_stats.lower():
                mappings.append((team_without_stats, team_with_stats, f"Base name match: {base_name}"))
                break
            
            # Partial match
            elif base_name.lower() in team_with_stats.lower() or team_with_stats.lower() in base_name.lower():
                # Only if it's a strong match (avoid false positives)
                if len(base_name) > 4 and abs(len(base_name) - len(team_with_stats)) < 5:
                    mappings.append((team_without_stats, team_with_stats, f"Partial match: {base_name} ≈ {team_with_stats}"))
                    break
    
    return mappings

def apply_config_mappings(league_name: str, config_mappings: Dict[str, str]) -> int:
    """Apply config mappings for a league."""
    if not config_mappings:
        return 0
    
    fixes_applied = 0
    
    with get_database() as db:
        for current_team, old_team in config_mappings.items():
            # Get team IDs
            current_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (current_team, league_name))
            
            old_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (old_team, league_name))
            
            if not current_id or not old_id:
                continue
            
            # Check stats
            current_stats = db.get_team_stats(current_team, league_name)
            old_stats = db.get_team_stats(old_team, league_name)
            
            if current_stats.empty and old_stats.empty:
                continue
            
            # Determine mapping direction
            if not current_stats.empty and old_stats.empty:
                target_id, source_id = current_id, old_id
            elif current_stats.empty and not old_stats.empty:
                target_id, source_id = old_id, current_id
            else:
                continue  # Both have stats, skip
            
            try:
                db.conn.execute(
                    'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                    (target_id, source_id)
                )
                fixes_applied += 1
            except Exception:
                continue
        
        if fixes_applied > 0:
            db.conn.commit()
    
    return fixes_applied

def apply_smart_mappings(league_name: str, mappings: List[Tuple[str, str, str]]) -> int:
    """Apply smart mappings for a league."""
    if not mappings:
        return 0
    
    fixes_applied = 0
    
    with get_database() as db:
        for old_team, target_team, reason in mappings:
            # Get team IDs
            old_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (old_team, league_name))
            
            target_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (target_team, league_name))
            
            if not old_id or not target_id:
                continue
            
            # Verify target has stats
            target_stats = db.get_team_stats(target_team, league_name)
            if target_stats.empty:
                continue
            
            try:
                db.conn.execute(
                    'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                    (target_id, old_id)
                )
                fixes_applied += 1
            except Exception:
                continue
        
        if fixes_applied > 0:
            db.conn.commit()
    
    return fixes_applied

def fix_circular_references(league_name: str) -> int:
    """Fix circular references in a league."""
    fixes_applied = 0
    
    with get_database() as db:
        # Find teams with stats that don't point to themselves
        problematic_teams = db.execute_query('''
            SELECT t.team_id, t.team_name, t.canonical_team_id, 
                   COUNT(ts.team_id) as stat_count
            FROM teams t
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ? AND t.team_id != t.canonical_team_id
            GROUP BY t.team_id, t.team_name, t.canonical_team_id
            HAVING stat_count > 0
        ''', (league_name,))
        
        for _, team in problematic_teams.iterrows():
            team_id = team['team_id']
            
            # Make the team canonical (point to itself)
            db.conn.execute(
                'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                (team_id, team_id)
            )
            fixes_applied += 1
        
        if fixes_applied > 0:
            db.conn.commit()
    
    return fixes_applied

def fix_single_league(league_name: str) -> Dict:
    """Fix a single league and return results."""
    print(f"\n🔧 FIXING: {league_name}")
    print("=" * 60)
    
    # Analyze initial state
    initial_state = analyze_league_state(league_name)
    if initial_state['total_teams'] == 0:
        print(f"❌ No teams found for {league_name}")
        return {'league_name': league_name, 'success': False, 'error': 'No teams found'}
    
    print(f"📊 Initial: {initial_state['working_teams']}/{initial_state['total_teams']} teams ({initial_state['success_rate']:.1f}%)")
    
    # Load config mappings
    config_mappings = load_league_config(league_name)
    config_fixes = apply_config_mappings(league_name, config_mappings)
    
    # Find and apply smart mappings
    smart_mappings = find_smart_mappings(league_name, initial_state['teams_info'])
    smart_fixes = apply_smart_mappings(league_name, smart_mappings)
    
    # Fix circular references
    circular_fixes = fix_circular_references(league_name)
    
    # Analyze final state
    final_state = analyze_league_state(league_name)
    
    total_fixes = config_fixes + smart_fixes + circular_fixes
    improvement = final_state['working_teams'] - initial_state['working_teams']
    
    print(f"✅ Applied {total_fixes} fixes ({config_fixes} config + {smart_fixes} smart + {circular_fixes} circular)")
    print(f"📈 Final: {final_state['working_teams']}/{final_state['total_teams']} teams ({final_state['success_rate']:.1f}%)")
    print(f"🎯 Improvement: +{improvement} teams (+{final_state['success_rate'] - initial_state['success_rate']:.1f}%)")
    
    return {
        'league_name': league_name,
        'success': True,
        'initial_working': initial_state['working_teams'],
        'initial_total': initial_state['total_teams'],
        'initial_rate': initial_state['success_rate'],
        'final_working': final_state['working_teams'],
        'final_total': final_state['total_teams'],
        'final_rate': final_state['success_rate'],
        'total_fixes': total_fixes,
        'improvement': improvement,
        'config_fixes': config_fixes,
        'smart_fixes': smart_fixes,
        'circular_fixes': circular_fixes
    }

def batch_fix_leagues(leagues: List[str]) -> List[Dict]:
    """Fix multiple leagues in batch."""
    print(f"🚀 BATCH FIXING {len(leagues)} LEAGUES")
    print("=" * 70)
    
    results = []
    
    for i, league_name in enumerate(leagues, 1):
        print(f"\n[{i}/{len(leagues)}] Processing: {league_name}")
        
        try:
            result = fix_single_league(league_name)
            results.append(result)
        except Exception as e:
            print(f"❌ Error fixing {league_name}: {e}")
            results.append({
                'league_name': league_name,
                'success': False,
                'error': str(e)
            })
    
    return results

def print_batch_summary(results: List[Dict]):
    """Print summary of batch results."""
    print(f"\n🎉 BATCH PROCESSING COMPLETE!")
    print("=" * 70)
    
    successful_results = [r for r in results if r.get('success', False)]
    failed_results = [r for r in results if not r.get('success', False)]
    
    print(f"📊 SUMMARY:")
    print(f"   Leagues processed: {len(results)}")
    print(f"   Successful: {len(successful_results)}")
    print(f"   Failed: {len(failed_results)}")
    
    if successful_results:
        total_teams_improved = sum(r['improvement'] for r in successful_results)
        total_fixes_applied = sum(r['total_fixes'] for r in successful_results)
        
        print(f"\n🎯 IMPROVEMENTS:")
        print(f"   Total teams fixed: {total_teams_improved}")
        print(f"   Total fixes applied: {total_fixes_applied}")
        
        print(f"\n📈 LEAGUE RESULTS:")
        print(f"{'League':<40} {'Before':<12} {'After':<12} {'Improvement':<12}")
        print("-" * 80)
        
        for result in successful_results:
            before = f"{result['initial_working']}/{result['initial_total']}"
            after = f"{result['final_working']}/{result['final_total']}"
            improvement = f"+{result['improvement']} teams"
            
            print(f"{result['league_name']:<40} {before:<12} {after:<12} {improvement:<12}")
    
    if failed_results:
        print(f"\n❌ FAILED LEAGUES:")
        for result in failed_results:
            print(f"   {result['league_name']}: {result.get('error', 'Unknown error')}")

def main():
    """Main function."""
    # Test with first 5 priority leagues
    leagues_to_fix = PRIORITY_LEAGUES[:5]
    
    print(f"🔧 BATCH LEAGUE FIXING SYSTEM")
    print(f"Testing with {len(leagues_to_fix)} leagues:")
    for league in leagues_to_fix:
        print(f"   - {league}")
    
    # Process leagues
    results = batch_fix_leagues(leagues_to_fix)
    
    # Print summary
    print_batch_summary(results)

if __name__ == "__main__":
    main()
