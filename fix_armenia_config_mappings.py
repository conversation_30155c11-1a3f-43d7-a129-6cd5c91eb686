#!/usr/bin/env python3
"""
Fix Armenia Premier League Config Mappings

This script applies the specific config mappings for Armenia Premier League
to fix the remaining canonical team mapping issues identified in the report:
- FC Avan Academy → Ararat Armenia
- FC Banants → Urartu (need to verify)
- FC Ararat Yerevan → Ararat Yerevan
"""

import sys
import importlib.util
from typing import Dict, List, Tuple

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

def load_armenia_config() -> Dict[str, str]:
    """Load the Armenia Premier League config mappings."""
    config_path = "src/scrapers/league_configs/ARMENIA_PREMIER_LEAGUE.py"
    
    try:
        spec = importlib.util.spec_from_file_location("config", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        
        # Get the current config
        current_config = getattr(config_module, 'CURRENT_CONFIG', None)
        if current_config and 'TEAM_NAME_MAPPING' in current_config:
            return current_config['TEAM_NAME_MAPPING']
        
        # Fallback: try to get TEAM_NAME_MAPPING directly
        team_mapping = getattr(config_module, 'TEAM_NAME_MAPPING', None)
        if team_mapping:
            return team_mapping
            
        return {}
    except Exception as e:
        print(f"❌ Error loading Armenia config: {e}")
        return {}

def analyze_current_state():
    """Analyze the current state of Armenia Premier League teams."""
    print("🔍 ANALYZING CURRENT ARMENIA PREMIER LEAGUE STATE")
    print("=" * 60)
    
    with get_database() as db:
        # Get all Armenia teams with their canonical mappings
        armenia_teams = db.execute_query('''
            SELECT t.team_name, t.team_id, t.canonical_team_id, 
                   t2.team_name as canonical_name
            FROM teams t
            LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = "ARMENIA_PREMIER_LEAGUE"
            ORDER BY t.team_name
        ''')
        
        if armenia_teams.empty:
            print("❌ No Armenia Premier League teams found")
            return {}
        
        print(f"📊 Found {len(armenia_teams)} teams in Armenia Premier League")
        
        # Check which teams have stats
        teams_info = {}
        for _, team in armenia_teams.iterrows():
            team_name = team['team_name']
            team_id = team['team_id']
            canonical_name = team['canonical_name']
            
            # Check if team has stats via get_team_stats (uses canonical resolution)
            stats = db.get_team_stats(team_name, "ARMENIA_PREMIER_LEAGUE")
            has_stats = not stats.empty
            
            teams_info[team_name] = {
                'team_id': team_id,
                'canonical_name': canonical_name,
                'has_stats': has_stats,
                'canonical_id': team['canonical_team_id']
            }
            
            status = "✅" if has_stats else "❌"
            canonical_info = f" → {canonical_name}" if canonical_name != team_name else ""
            print(f"   {status} {team_name}{canonical_info}")
        
        return teams_info

def apply_config_mappings(config_mappings: Dict[str, str], teams_info: Dict):
    """Apply the config mappings to fix canonical team mappings."""
    print("\n🔧 APPLYING CONFIG MAPPINGS")
    print("=" * 50)
    
    fixes_applied = 0
    
    with get_database() as db:
        for current_team, old_team in config_mappings.items():
            print(f"\n📋 Processing: {current_team} ← {old_team}")
            
            # Check if both teams exist
            if current_team not in teams_info:
                print(f"   ⚠️  Current team '{current_team}' not found in database")
                continue
            
            if old_team not in teams_info:
                print(f"   ⚠️  Old team '{old_team}' not found in database")
                continue
            
            current_info = teams_info[current_team]
            old_info = teams_info[old_team]
            
            current_team_id = current_info['team_id']
            old_team_id = old_info['team_id']
            old_canonical_id = old_info['canonical_id']
            
            print(f"   Current team: {current_team} (id: {current_team_id})")
            print(f"   Old team: {old_team} (id: {old_team_id}, canonical_id: {old_canonical_id})")
            
            # Check if old team already points to current team
            if old_canonical_id == current_team_id:
                print(f"   ✅ Already correctly mapped")
                continue
            
            # Check if current team has stats and old team doesn't
            current_has_stats = current_info['has_stats']
            old_has_stats = old_info['has_stats']
            
            print(f"   Stats - Current: {current_has_stats}, Old: {old_has_stats}")
            
            if not current_has_stats and not old_has_stats:
                print(f"   ⚠️  Neither team has stats - skipping")
                continue
            
            if old_has_stats and not current_has_stats:
                print(f"   ⚠️  Old team has stats but current doesn't - this might be backwards")
                # In this case, we might want to map current → old instead
                print(f"   🔄 Considering reverse mapping: {current_team} → {old_team}")
                
                try:
                    db.conn.execute(
                        "UPDATE teams SET canonical_team_id = ? WHERE team_id = ?",
                        (old_team_id, current_team_id)
                    )
                    print(f"   ✅ Applied reverse mapping: {current_team} → {old_team}")
                    fixes_applied += 1
                except Exception as e:
                    print(f"   ❌ Error applying reverse mapping: {e}")
                continue
            
            # Standard mapping: old → current
            try:
                db.conn.execute(
                    "UPDATE teams SET canonical_team_id = ? WHERE team_id = ?",
                    (current_team_id, old_team_id)
                )
                print(f"   ✅ Applied mapping: {old_team} → {current_team}")
                fixes_applied += 1
            except Exception as e:
                print(f"   ❌ Error applying mapping: {e}")
        
        if fixes_applied > 0:
            db.conn.commit()
            print(f"\n💾 Committed {fixes_applied} fixes to database")
        else:
            print(f"\n✅ No fixes needed - all mappings already correct")
    
    return fixes_applied

def test_fixes():
    """Test that the fixes are working correctly."""
    print("\n🧪 TESTING APPLIED FIXES")
    print("=" * 40)
    
    # Test cases based on the config mappings
    test_cases = [
        ("FC Avan Academy", "Should get Ararat Armenia's stats"),
        ("FC Banants", "Should get mapped team's stats"),
        ("FC Ararat Yerevan", "Should get Ararat Yerevan's stats"),
        ("Artsakh", "Should get Noah's stats (already working)"),
    ]
    
    with get_database() as db:
        for team_name, expected in test_cases:
            print(f"\n🔍 Testing: {team_name}")
            
            # Test canonical resolution
            canonical = db._get_canonical_team_name(team_name, "ARMENIA_PREMIER_LEAGUE")
            print(f"   Canonical name: {canonical}")
            
            # Test team stats
            stats = db.get_team_stats(team_name, "ARMENIA_PREMIER_LEAGUE")
            if not stats.empty:
                ppg = stats.iloc[0].get('points_per_game', 'N/A')
                print(f"   ✅ Has stats: PPG = {ppg}")
            else:
                print(f"   ❌ No stats found")
            
            # Test team form
            form = db.get_team_form(team_name, "ARMENIA_PREMIER_LEAGUE", 3)
            print(f"   Form matches: {len(form)}")

def verify_all_armenia_teams():
    """Verify the final state of all Armenia teams."""
    print("\n📊 FINAL ARMENIA PREMIER LEAGUE STATUS")
    print("=" * 50)
    
    with get_database() as db:
        armenia_teams = db.execute_query('''
            SELECT t.team_name, t2.team_name as canonical_name
            FROM teams t
            LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = "ARMENIA_PREMIER_LEAGUE"
            ORDER BY t.team_name
        ''')
        
        working_teams = 0
        total_teams = len(armenia_teams)
        
        for _, team in armenia_teams.iterrows():
            team_name = team['team_name']
            canonical_name = team['canonical_name']
            
            # Test if team has stats
            stats = db.get_team_stats(team_name, "ARMENIA_PREMIER_LEAGUE")
            has_stats = not stats.empty
            
            if has_stats:
                working_teams += 1
            
            status = "✅" if has_stats else "❌"
            canonical_info = f" → {canonical_name}" if canonical_name != team_name else ""
            print(f"   {status} {team_name}{canonical_info}")
        
        success_rate = (working_teams / total_teams) * 100
        print(f"\n📈 Final Results:")
        print(f"   Working teams: {working_teams}/{total_teams}")
        print(f"   Success rate: {success_rate:.1f}%")
        
        return working_teams, total_teams

def main():
    """Main function."""
    print("🇦🇲 ARMENIA PREMIER LEAGUE CONFIG MAPPING FIX")
    print("=" * 60)
    print("Applying config mappings to fix remaining canonical team issues")
    
    # Load config mappings
    config_mappings = load_armenia_config()
    if not config_mappings:
        print("❌ No config mappings found")
        return
    
    print(f"📋 Found {len(config_mappings)} config mappings:")
    for current, old in config_mappings.items():
        print(f"   {current} ← {old}")
    
    # Analyze current state
    teams_info = analyze_current_state()
    if not teams_info:
        return
    
    # Apply fixes
    fixes_applied = apply_config_mappings(config_mappings, teams_info)
    
    # Test fixes
    test_fixes()
    
    # Verify final state
    working, total = verify_all_armenia_teams()
    
    # Summary
    print(f"\n🎉 ARMENIA PREMIER LEAGUE FIX COMPLETE!")
    print(f"Applied {fixes_applied} canonical mapping fixes")
    print(f"Final success rate: {(working/total)*100:.1f}% ({working}/{total} teams)")
    
    if working == total:
        print("🎯 Perfect! All Armenia Premier League teams now have stats!")
    else:
        remaining = total - working
        print(f"⚠️  {remaining} teams still need attention")

if __name__ == "__main__":
    main()
