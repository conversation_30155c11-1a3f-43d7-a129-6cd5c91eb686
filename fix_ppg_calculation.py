#!/usr/bin/env python3
"""
Fix Points Per Game Calculation Issue

This script fixes the overall points_per_game calculation for teams that have
None or 0.00 values despite having valid home/away PPG values and match data.

The script will:
1. Find teams with missing overall PPG but valid home/away PPG
2. Calculate overall PPG using two methods:
   - Method 1: (home_ppg + away_ppg) / 2
   - Method 2: (total_wins * 3 + total_draws) / total_played
3. Use the more accurate method based on available data
4. Update the database with the corrected values
"""

import sys
import sqlite3
import os
from typing import List, Tuple, Optional

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

def find_teams_with_missing_ppg() -> List[Tuple]:
    """Find teams with missing overall PPG but valid home/away PPG or match data."""
    with get_database() as db:
        # Find teams where overall PPG is None but home/away PPG exist
        query = '''
            SELECT 
                ts.stat_id,
                t.team_name,
                l.league_name,
                ts.points_per_game,
                ts.home_points_per_game,
                ts.away_points_per_game,
                ts.total_played,
                ts.total_wins,
                ts.total_draws,
                ts.total_losses
            FROM team_stats ts
            JOIN teams t ON ts.team_id = t.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE (ts.points_per_game IS NULL OR ts.points_per_game = 0.0)
            AND (
                (ts.home_points_per_game IS NOT NULL AND ts.away_points_per_game IS NOT NULL)
                OR (ts.total_wins IS NOT NULL AND ts.total_draws IS NOT NULL AND ts.total_played IS NOT NULL AND ts.total_played > 0)
            )
            ORDER BY l.league_name, t.team_name
        '''
        
        result = db.execute_query(query)
        if result.empty:
            return []
        
        return result.to_dict('records')

def calculate_ppg(team_data: dict) -> Optional[float]:
    """Calculate PPG using the best available method."""
    
    # Method 1: Calculate from wins/draws/total_played (most accurate)
    if (team_data['total_wins'] is not None and 
        team_data['total_draws'] is not None and 
        team_data['total_played'] is not None and 
        team_data['total_played'] > 0):
        
        total_points = (team_data['total_wins'] * 3) + (team_data['total_draws'] * 1)
        ppg_from_matches = total_points / team_data['total_played']
        
        # Method 2: Average of home/away PPG (if available)
        ppg_from_home_away = None
        if (team_data['home_points_per_game'] is not None and 
            team_data['away_points_per_game'] is not None):
            ppg_from_home_away = (team_data['home_points_per_game'] + team_data['away_points_per_game']) / 2
        
        # Use the match-based calculation as it's more accurate
        # But validate against home/away average if available
        if ppg_from_home_away is not None:
            # If the difference is significant, log it for review
            diff = abs(ppg_from_matches - ppg_from_home_away)
            if diff > 0.5:  # More than 0.5 PPG difference
                print(f"  ⚠️  Large difference detected for {team_data['team_name']}: "
                      f"Match-based: {ppg_from_matches:.2f}, Home/Away avg: {ppg_from_home_away:.2f}")
        
        return round(ppg_from_matches, 2)
    
    # Method 2: Average of home/away PPG (fallback)
    elif (team_data['home_points_per_game'] is not None and 
          team_data['away_points_per_game'] is not None):
        ppg = (team_data['home_points_per_game'] + team_data['away_points_per_game']) / 2
        return round(ppg, 2)
    
    return None

def update_team_ppg(stat_id: int, new_ppg: float) -> bool:
    """Update the points_per_game for a specific team stat record."""
    try:
        with get_database() as db:
            db.conn.execute(
                "UPDATE team_stats SET points_per_game = ? WHERE stat_id = ?",
                (new_ppg, stat_id)
            )
            db.conn.commit()
            return True
    except Exception as e:
        print(f"Error updating stat_id {stat_id}: {e}")
        return False

def main():
    """Main function to fix PPG calculations."""
    print("🔧 Football Database PPG Fix")
    print("=" * 50)
    
    # Find teams with missing PPG
    print("🔍 Finding teams with missing overall PPG...")
    teams_to_fix = find_teams_with_missing_ppg()
    
    if not teams_to_fix:
        print("✅ No teams found with missing PPG values!")
        return
    
    print(f"📊 Found {len(teams_to_fix)} teams with missing overall PPG")
    print()
    
    # Process each team
    fixed_count = 0
    error_count = 0
    
    for team_data in teams_to_fix:
        team_name = team_data['team_name']
        league_name = team_data['league_name']
        
        print(f"🏈 Processing: {team_name} ({league_name})")
        print(f"   Current PPG: {team_data['points_per_game']}")
        print(f"   Home PPG: {team_data['home_points_per_game']}")
        print(f"   Away PPG: {team_data['away_points_per_game']}")
        print(f"   Record: {team_data['total_wins']}W-{team_data['total_draws']}D-{team_data['total_losses']}L ({team_data['total_played']} played)")
        
        # Calculate new PPG
        new_ppg = calculate_ppg(team_data)
        
        if new_ppg is not None:
            print(f"   ✅ Calculated PPG: {new_ppg}")
            
            # Update database
            if update_team_ppg(team_data['stat_id'], new_ppg):
                print(f"   💾 Updated database successfully")
                fixed_count += 1
            else:
                print(f"   ❌ Failed to update database")
                error_count += 1
        else:
            print(f"   ❌ Could not calculate PPG - insufficient data")
            error_count += 1
        
        print()
    
    # Summary
    print("📈 PPG Fix Summary")
    print("-" * 30)
    print(f"✅ Teams fixed: {fixed_count}")
    print(f"❌ Errors: {error_count}")
    print(f"📊 Total processed: {len(teams_to_fix)}")
    
    if fixed_count > 0:
        print(f"\n🎉 Successfully fixed PPG calculation for {fixed_count} teams!")
    
    # Test with Leeds Utd specifically
    print("\n🧪 Testing Leeds Utd fix...")
    with get_database() as db:
        stats = db.get_team_stats('Leeds Utd', 'ENGLAND_CHAMPIONSHIP')
        if not stats.empty:
            row = stats.iloc[0]
            print(f"Leeds Utd PPG after fix: {row['points_per_game']}")
        else:
            print("Leeds Utd not found")

if __name__ == "__main__":
    main()
