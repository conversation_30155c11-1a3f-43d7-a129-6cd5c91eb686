#!/usr/bin/env python3
"""
Comprehensive Batch Fix All Leagues

This script processes ALL leagues in batches of 50, excluding failed ones,
until every league has been attempted. It runs continuously without pausing.
"""

import sys
import importlib.util
import os
from typing import Dict, List, Tuple, Set
from collections import defaultdict

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

# ============================================================================
# CONFIGURATION
# ============================================================================

# Leagues that failed in previous batches (no teams found)
FAILED_LEAGUES = {
    "SCOTLAND_HIGHLAND_FOOTBALL_LEAGUE",
    "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_E",
    "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_F", 
    "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_G",
    "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_H",
    "SCOTLAND_LOWLAND_FOOTBALL_LEAGUE",
    "GERMANY_OBERLIGA_BAYERN"
}

# Leagues already processed successfully
PROCESSED_LEAGUES = {
    "ALBANIA_SUPERLIGA",
    "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_C",
    "MALTA_FIRST_DIVISION",
    "GERMANY_OBERLIGA_NIEDERRHEIN", 
    "IRAN_PERSIAN_GULF_PRO_LEAGUE",
    "ENGLAND_PROFESSIONAL_DEVELOPMENT_LEAGUE",
    "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_A",
    "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_B",
    "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_D",
    "GERMANY_OBERLIGA_WESTFALEN",
    "GERMANY_OBERLIGA_RHEINLAND_PFALZ_SAAR",
    "GERMANY_OBERLIGA_BADEN_WURTTEMBERG", 
    "GERMANY_OBERLIGA_HAMBURG"
}

BATCH_SIZE = 50

# ============================================================================
# HELPER FUNCTIONS (Same as before)
# ============================================================================

def load_league_config(league_name: str) -> Dict[str, str]:
    """Load config mappings for a league."""
    config_path = f"src/scrapers/league_configs/{league_name}.py"
    
    try:
        spec = importlib.util.spec_from_file_location("config", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        
        current_config = getattr(config_module, 'CURRENT_CONFIG', None)
        if current_config and 'TEAM_NAME_MAPPING' in current_config:
            return current_config['TEAM_NAME_MAPPING']
        
        team_mapping = getattr(config_module, 'TEAM_NAME_MAPPING', None)
        if team_mapping:
            return team_mapping
            
        return {}
    except Exception:
        return {}

def analyze_league_state(league_name: str) -> Dict:
    """Analyze current state of a league."""
    with get_database() as db:
        teams = db.execute_query('''
            SELECT t.team_name, t.team_id, t.canonical_team_id, 
                   t2.team_name as canonical_name
            FROM teams t
            LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY t.team_name
        ''', (league_name,))
        
        if teams.empty:
            return {'total_teams': 0, 'working_teams': 0, 'teams_info': {}}
        
        teams_info = {}
        working_teams = 0
        
        for _, team in teams.iterrows():
            team_name = team['team_name']
            team_id = team['team_id']
            canonical_name = team['canonical_name']
            
            stats = db.get_team_stats(team_name, league_name)
            has_stats = not stats.empty
            
            if has_stats:
                working_teams += 1
            
            teams_info[team_name] = {
                'team_id': team_id,
                'canonical_name': canonical_name,
                'has_stats': has_stats,
                'canonical_id': team['canonical_team_id']
            }
        
        return {
            'total_teams': len(teams),
            'working_teams': working_teams,
            'teams_info': teams_info,
            'success_rate': (working_teams / len(teams)) * 100 if len(teams) > 0 else 0
        }

def find_smart_mappings(teams_info: Dict) -> List[Tuple[str, str, str]]:
    """Find smart mappings using pattern matching."""
    mappings = []
    
    teams_without_stats = [name for name, info in teams_info.items() if not info['has_stats']]
    teams_with_stats = [name for name, info in teams_info.items() if info['has_stats']]
    
    for team_without_stats in teams_without_stats:
        base_name = team_without_stats
        
        # Remove common prefixes/suffixes
        for prefix in ['KF ', 'FC ', 'AF ', 'KS ', 'Kf ', 'Fc ', 'Af ', 'Ks ', 'CF ', 'SC ', 'AC ', 'AS ']:
            if base_name.startswith(prefix):
                base_name = base_name[len(prefix):]
                break
        
        for suffix in [' FC', ' KF', ' AF', ' KS', ' CF', ' SC', ' AC', ' AS']:
            if base_name.endswith(suffix):
                base_name = base_name[:-len(suffix)]
                break
        
        # Look for matches in teams with stats
        for team_with_stats in teams_with_stats:
            if base_name.lower() == team_with_stats.lower():
                mappings.append((team_without_stats, team_with_stats, f"Base name match: {base_name}"))
                break
            elif base_name.lower() in team_with_stats.lower() or team_with_stats.lower() in base_name.lower():
                if len(base_name) > 4 and abs(len(base_name) - len(team_with_stats)) < 5:
                    mappings.append((team_without_stats, team_with_stats, f"Partial match: {base_name} ≈ {team_with_stats}"))
                    break
    
    return mappings

def apply_config_mappings(league_name: str, config_mappings: Dict[str, str]) -> int:
    """Apply config mappings for a league."""
    if not config_mappings:
        return 0
    
    fixes_applied = 0
    
    with get_database() as db:
        for current_team, old_team in config_mappings.items():
            current_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (current_team, league_name))
            
            old_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (old_team, league_name))
            
            if not current_id or not old_id:
                continue
            
            current_stats = db.get_team_stats(current_team, league_name)
            old_stats = db.get_team_stats(old_team, league_name)
            
            if current_stats.empty and old_stats.empty:
                continue
            
            if not current_stats.empty and old_stats.empty:
                target_id, source_id = current_id, old_id
            elif current_stats.empty and not old_stats.empty:
                target_id, source_id = old_id, current_id
            else:
                continue
            
            try:
                db.conn.execute(
                    'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                    (target_id, source_id)
                )
                fixes_applied += 1
            except Exception:
                continue
        
        if fixes_applied > 0:
            db.conn.commit()
    
    return fixes_applied

def apply_smart_mappings(league_name: str, mappings: List[Tuple[str, str, str]]) -> int:
    """Apply smart mappings for a league."""
    if not mappings:
        return 0
    
    fixes_applied = 0
    
    with get_database() as db:
        for old_team, target_team, _ in mappings:
            old_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (old_team, league_name))
            
            target_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (target_team, league_name))
            
            if not old_id or not target_id:
                continue
            
            target_stats = db.get_team_stats(target_team, league_name)
            if target_stats.empty:
                continue
            
            try:
                db.conn.execute(
                    'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                    (target_id, old_id)
                )
                fixes_applied += 1
            except Exception:
                continue
        
        if fixes_applied > 0:
            db.conn.commit()
    
    return fixes_applied

def fix_circular_references(league_name: str) -> int:
    """Fix circular references in a league."""
    fixes_applied = 0
    
    with get_database() as db:
        problematic_teams = db.execute_query('''
            SELECT t.team_id, t.team_name, t.canonical_team_id, 
                   COUNT(ts.team_id) as stat_count
            FROM teams t
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ? AND t.team_id != t.canonical_team_id
            GROUP BY t.team_id, t.team_name, t.canonical_team_id
            HAVING stat_count > 0
        ''', (league_name,))
        
        for _, team in problematic_teams.iterrows():
            team_id = team['team_id']
            
            db.conn.execute(
                'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                (team_id, team_id)
            )
            fixes_applied += 1
        
        if fixes_applied > 0:
            db.conn.commit()
    
    return fixes_applied

def fix_single_league(league_name: str) -> Dict:
    """Fix a single league and return results."""
    # Analyze initial state
    initial_state = analyze_league_state(league_name)
    if initial_state['total_teams'] == 0:
        return {'league_name': league_name, 'success': False, 'error': 'No teams found'}

    # Load config mappings
    config_mappings = load_league_config(league_name)
    config_fixes = apply_config_mappings(league_name, config_mappings)

    # Find and apply smart mappings
    smart_mappings = find_smart_mappings(initial_state['teams_info'])
    smart_fixes = apply_smart_mappings(league_name, smart_mappings)

    # Fix circular references
    circular_fixes = fix_circular_references(league_name)

    # Analyze final state
    final_state = analyze_league_state(league_name)

    total_fixes = config_fixes + smart_fixes + circular_fixes
    improvement = final_state['working_teams'] - initial_state['working_teams']

    return {
        'league_name': league_name,
        'success': True,
        'initial_working': initial_state['working_teams'],
        'initial_total': initial_state['total_teams'],
        'initial_rate': initial_state['success_rate'],
        'final_working': final_state['working_teams'],
        'final_total': final_state['total_teams'],
        'final_rate': final_state['success_rate'],
        'total_fixes': total_fixes,
        'improvement': improvement,
        'config_fixes': config_fixes,
        'smart_fixes': smart_fixes,
        'circular_fixes': circular_fixes
    }

def get_all_leagues() -> List[str]:
    """Get all leagues from the database."""
    with get_database() as db:
        leagues = db.execute_query("SELECT league_name FROM leagues ORDER BY league_name")
        return leagues['league_name'].tolist() if not leagues.empty else []

def get_remaining_leagues() -> List[str]:
    """Get leagues that haven't been processed yet."""
    all_leagues = get_all_leagues()
    remaining = [league for league in all_leagues
                if league not in FAILED_LEAGUES and league not in PROCESSED_LEAGUES]
    return remaining

def process_batch(leagues: List[str], batch_num: int, total_batches: int) -> List[Dict]:
    """Process a batch of leagues."""
    print(f"\n🚀 BATCH {batch_num}/{total_batches}: PROCESSING {len(leagues)} LEAGUES")
    print("=" * 80)

    results = []
    batch_teams_improved = 0
    batch_fixes_applied = 0

    for i, league_name in enumerate(leagues, 1):
        print(f"[{i:2d}/{len(leagues)}] {league_name}")

        try:
            result = fix_single_league(league_name)
            results.append(result)

            if result.get('success', False):
                improvement = result.get('improvement', 0)
                fixes = result.get('total_fixes', 0)
                batch_teams_improved += improvement
                batch_fixes_applied += fixes

                # Show brief result
                initial = f"{result['initial_working']}/{result['initial_total']}"
                final = f"{result['final_working']}/{result['final_total']}"
                if improvement > 0:
                    print(f"    ✅ {initial} → {final} (+{improvement} teams, {fixes} fixes)")
                else:
                    print(f"    ⚪ {initial} → {final} ({fixes} fixes)")

        except Exception as e:
            print(f"    ❌ Error: {e}")
            results.append({
                'league_name': league_name,
                'success': False,
                'error': str(e)
            })

    print(f"\n📊 BATCH {batch_num} SUMMARY:")
    print(f"   Teams improved: {batch_teams_improved}")
    print(f"   Fixes applied: {batch_fixes_applied}")

    return results

def print_comprehensive_summary(all_results: List[List[Dict]]):
    """Print comprehensive summary of all batches."""
    print(f"\n🎉 COMPREHENSIVE BATCH PROCESSING COMPLETE!")
    print("=" * 80)

    # Flatten results
    all_league_results = []
    for batch_results in all_results:
        all_league_results.extend(batch_results)

    successful_results = [r for r in all_league_results if r.get('success', False)]
    failed_results = [r for r in all_league_results if not r.get('success', False)]

    total_teams_improved = sum(r.get('improvement', 0) for r in successful_results)
    total_fixes_applied = sum(r.get('total_fixes', 0) for r in successful_results)

    print(f"📊 OVERALL STATISTICS:")
    print(f"   Total leagues processed: {len(all_league_results)}")
    print(f"   Successful: {len(successful_results)}")
    print(f"   Failed (no teams): {len(failed_results)}")
    print(f"   Total teams improved: {total_teams_improved}")
    print(f"   Total fixes applied: {total_fixes_applied}")

    # Show top performers
    top_performers = sorted([r for r in successful_results if r.get('improvement', 0) > 0],
                           key=lambda x: x.get('improvement', 0), reverse=True)[:10]

    if top_performers:
        print(f"\n🏆 TOP 10 PERFORMING LEAGUES:")
        print(f"{'League':<50} {'Before':<12} {'After':<12} {'Improvement':<12}")
        print("-" * 90)

        for result in top_performers:
            before = f"{result['initial_working']}/{result['initial_total']}"
            after = f"{result['final_working']}/{result['final_total']}"
            improvement = f"+{result['improvement']} teams"

            print(f"{result['league_name']:<50} {before:<12} {after:<12} {improvement:<12}")

    # Show leagues that need investigation
    no_improvement = [r for r in successful_results if r.get('improvement', 0) == 0 and r.get('total_fixes', 0) > 0]
    if no_improvement:
        print(f"\n⚠️  LEAGUES NEEDING INVESTIGATION ({len(no_improvement)} leagues applied fixes but no improvement):")
        for result in no_improvement[:10]:  # Show first 10
            fixes = result.get('total_fixes', 0)
            rate = result.get('final_rate', 0)
            print(f"   {result['league_name']}: {fixes} fixes applied, {rate:.1f}% success rate")

def main():
    """Main function - process all remaining leagues in batches of 50."""
    print(f"🔧 COMPREHENSIVE BATCH LEAGUE FIXING SYSTEM")
    print("=" * 80)

    # Get remaining leagues
    remaining_leagues = get_remaining_leagues()

    if not remaining_leagues:
        print("✅ All leagues have been processed!")
        return

    print(f"📊 PROCESSING OVERVIEW:")
    print(f"   Total leagues in database: {len(get_all_leagues())}")
    print(f"   Already processed: {len(PROCESSED_LEAGUES)}")
    print(f"   Failed (no teams): {len(FAILED_LEAGUES)}")
    print(f"   Remaining to process: {len(remaining_leagues)}")

    # Calculate batches
    total_batches = (len(remaining_leagues) + BATCH_SIZE - 1) // BATCH_SIZE
    print(f"   Batches needed: {total_batches} (batch size: {BATCH_SIZE})")

    # Process all batches
    all_results = []

    for batch_num in range(1, total_batches + 1):
        start_idx = (batch_num - 1) * BATCH_SIZE
        end_idx = min(start_idx + BATCH_SIZE, len(remaining_leagues))
        batch_leagues = remaining_leagues[start_idx:end_idx]

        batch_results = process_batch(batch_leagues, batch_num, total_batches)
        all_results.append(batch_results)

    # Print comprehensive summary
    print_comprehensive_summary(all_results)

if __name__ == "__main__":
    main()
