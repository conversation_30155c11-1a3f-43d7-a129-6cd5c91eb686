#!/usr/bin/env python3
"""
Run League Fixes Script

This script systematically runs all the individual league fix scripts
to improve team mapping success rates across all leagues.

Based on the LEAGUE_FIXING_GUIDE.md and team_mapping_issues_report.md
"""

import sys
import subprocess
import os
from typing import List, Tuple

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

def get_current_league_status(league_name: str) -> Tuple[int, int, float]:
    """Get current status of a league."""
    with get_database() as db:
        teams = db.execute_query('''
            SELECT t.team_name FROM teams t
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
        ''', (league_name,))
        
        if teams.empty:
            return 0, 0, 0.0
        
        working_teams = 0
        total_teams = len(teams)
        
        for _, team in teams.iterrows():
            stats = db.get_team_stats(team['team_name'], league_name)
            if not stats.empty:
                working_teams += 1
        
        success_rate = (working_teams / total_teams) * 100 if total_teams > 0 else 0
        return working_teams, total_teams, success_rate

def run_fix_script(script_name: str) -> bool:
    """Run a fix script and return success status."""
    if not os.path.exists(script_name):
        print(f"Script {script_name} not found")
        return False
    
    try:
        print(f"\nRunning {script_name}...")
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"{script_name} completed successfully")
            return True
        else:
            print(f"{script_name} failed with return code {result.returncode}")
            if result.stderr:
                print(f"Error: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"{script_name} timed out")
        return False
    except Exception as e:
        print(f"Error running {script_name}: {e}")
        return False

def main():
    """Main function to run all league fixes."""
    print("RUNNING ALL LEAGUE FIXES")
    print("=" * 70)
    print("This will systematically fix team mapping issues across all leagues")
    print("Based on the League Fixing Guide and team mapping issues report")
    
    # Define the fix scripts to run in priority order
    fix_scripts = [
        # High priority leagues with good success rates that can be improved
        ("fix_ANDORRA_PRIMERA_DIVISIO.py", "ANDORRA_PRIMERA_DIVISIO"),
        ("fix_ARMENIA_PREMIER_LEAGUE.py", "ARMENIA_PREMIER_LEAGUE"),
        ("fix_AUSTRIA_2_LIGA.py", "AUSTRIA_2_LIGA"),
        ("fix_AUSTRIA_BUNDESLIGA.py", "AUSTRIA_BUNDESLIGA"),
        ("fix_BAHRAIN_PREMIER_LEAGUE.py", "BAHRAIN_PREMIER_LEAGUE"),
        
        # Existing scripts that may need re-running
        ("fix_ARGENTINA_PRIMERA_DIVISION.py", "ARGENTINA_PRIMERA_DIVISION"),
        ("fix_AUSTRALIA_A_LEAGUE.py", "AUSTRALIA_A_LEAGUE"),
        ("fix_AUSTRALIA_A_LEAGUE_WOMEN.py", "AUSTRALIA_A_LEAGUE_WOMEN"),
        ("fix_AUSTRALIA_CAPITAL_TERRITORY_NPL.py", "AUSTRALIA_CAPITAL_TERRITORY_NPL"),
        ("fix_BELGIUM_NATIONAL_DIVISION_1.py", "BELGIUM_NATIONAL_DIVISION_1"),
    ]
    
    results = []
    successful_fixes = 0
    
    print(f"\nINITIAL STATUS CHECK")
    print("=" * 50)
    
    # Check initial status
    for script_name, league_name in fix_scripts:
        working, total, success_rate = get_current_league_status(league_name)
        print(f"{league_name}: {working}/{total} ({success_rate:.1f}%)")
    
    print(f"\nRUNNING FIXES")
    print("=" * 30)
    
    # Run each fix script
    for script_name, league_name in fix_scripts:
        print(f"\n{'='*70}")
        print(f"FIXING {league_name}")
        print(f"{'='*70}")
        
        # Get before status
        before_working, before_total, before_rate = get_current_league_status(league_name)
        print(f"Before: {before_working}/{before_total} ({before_rate:.1f}%)")
        
        # Run the fix
        success = run_fix_script(script_name)
        
        # Get after status
        after_working, after_total, after_rate = get_current_league_status(league_name)
        print(f"After:  {after_working}/{after_total} ({after_rate:.1f}%)")
        
        improvement = after_rate - before_rate
        improvement_str = f"(+{improvement:.1f}%)" if improvement > 0 else f"({improvement:.1f}%)" if improvement < 0 else "(no change)"
        
        results.append({
            'league': league_name,
            'script': script_name,
            'success': success,
            'before_rate': before_rate,
            'after_rate': after_rate,
            'improvement': improvement,
            'working': after_working,
            'total': after_total
        })
        
        if success:
            successful_fixes += 1
            print(f"{league_name} fix completed {improvement_str}")
        else:
            print(f"{league_name} fix failed")
    
    # Final summary
    print(f"\n{'='*70}")
    print(f"FINAL SUMMARY")
    print(f"{'='*70}")
    print(f"Scripts run: {len(fix_scripts)}")
    print(f"Successful: {successful_fixes}")
    print(f"Failed: {len(fix_scripts) - successful_fixes}")
    
    print(f"\nRESULTS BY LEAGUE:")
    print("-" * 70)
    
    total_improvement = 0
    leagues_improved = 0
    leagues_perfect = 0
    
    for result in results:
        status = "SUCCESS" if result['success'] else "FAILED"
        improvement = result['improvement']
        improvement_str = f"(+{improvement:.1f}%)" if improvement > 0 else f"({improvement:.1f}%)" if improvement < 0 else "(no change)"
        
        if result['after_rate'] >= 100:
            status += " PERFECT"
            leagues_perfect += 1
        elif improvement > 0:
            leagues_improved += 1
        
        print(f"{status} {result['league']}: {result['working']}/{result['total']} "
              f"({result['after_rate']:.1f}%) {improvement_str}")
        
        total_improvement += improvement
    
    avg_improvement = total_improvement / len(results) if results else 0
    
    print(f"\nOVERALL IMPACT:")
    print(f"   Average improvement: {avg_improvement:.1f}%")
    print(f"   Leagues improved: {leagues_improved}")
    print(f"   Perfect leagues (100%): {leagues_perfect}")
    print(f"   Total teams now working: {sum(r['working'] for r in results)}")
    print(f"   Total teams: {sum(r['total'] for r in results)}")
    
    if leagues_perfect > 0:
        print(f"\nPerfect leagues:")
        for result in results:
            if result['after_rate'] >= 100:
                print(f"   {result['league']}")

if __name__ == "__main__":
    main()