#!/usr/bin/env python3
"""
Fix Armenia Premier League Specific Mappings

Based on the analysis, we need to fix these specific mappings:
1. FC Avan Academy → Ararat Armenia (config shows: Ararat Armenia ← Fc Avan Academy)
2. FC Banants → Urartu (config shows: Urartu ← Fc Banants) 
3. Ararat → Ararat Yerevan (logical mapping)
4. FC Artsakh → Noah (via Artsakh, but direct mapping might be better)
"""

import sys
from typing import Dict, List, Tuple

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

def apply_specific_armenia_fixes():
    """Apply the specific fixes needed for Armenia Premier League."""
    print("🇦🇲 APPLYING SPECIFIC ARMENIA PREMIER LEAGUE FIXES")
    print("=" * 60)
    
    # Define the specific mappings we need to fix
    # Format: (old_team, target_team, reason)
    mappings_to_fix = [
        ("FC Avan Academy", "Ararat Armenia", "Config mapping: Ararat Armenia ← Fc Avan Academy"),
        ("FC Banants", "Urartu", "Config mapping: Urartu ← Fc Banants"),
        ("Ararat", "Ararat Yerevan", "Logical mapping to team with stats"),
        ("FC Artsakh", "Noah", "Direct mapping instead of via Artsakh"),
    ]
    
    fixes_applied = 0
    
    with get_database() as db:
        for old_team, target_team, reason in mappings_to_fix:
            print(f"\n🔧 Fixing: {old_team} → {target_team}")
            print(f"   Reason: {reason}")
            
            # Get old team info
            old_team_query = db.execute_query('''
                SELECT t.team_id, t.canonical_team_id, t2.team_name as current_canonical
                FROM teams t
                LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = "ARMENIA_PREMIER_LEAGUE"
            ''', (old_team,))
            
            if old_team_query.empty:
                print(f"   ❌ Old team '{old_team}' not found")
                continue
            
            # Get target team info
            target_team_query = db.execute_query('''
                SELECT t.team_id
                FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = "ARMENIA_PREMIER_LEAGUE"
            ''', (target_team,))
            
            if target_team_query.empty:
                print(f"   ❌ Target team '{target_team}' not found")
                continue
            
            old_team_id = old_team_query.iloc[0]['team_id']
            old_canonical_id = old_team_query.iloc[0]['canonical_team_id']
            current_canonical = old_team_query.iloc[0]['current_canonical']
            target_team_id = target_team_query.iloc[0]['team_id']
            
            print(f"   Old team ID: {old_team_id}")
            print(f"   Current canonical: {current_canonical} (ID: {old_canonical_id})")
            print(f"   Target team ID: {target_team_id}")
            
            # Check if already correctly mapped
            if old_canonical_id == target_team_id:
                print(f"   ✅ Already correctly mapped")
                continue
            
            # Check if target team has stats
            target_stats = db.get_team_stats(target_team, "ARMENIA_PREMIER_LEAGUE")
            if target_stats.empty:
                print(f"   ⚠️  Target team '{target_team}' has no stats")
                continue
            
            # Apply the fix
            try:
                db.conn.execute(
                    "UPDATE teams SET canonical_team_id = ? WHERE team_id = ?",
                    (target_team_id, old_team_id)
                )
                print(f"   ✅ Applied mapping: {old_team} → {target_team}")
                fixes_applied += 1
            except Exception as e:
                print(f"   ❌ Error applying mapping: {e}")
        
        if fixes_applied > 0:
            db.conn.commit()
            print(f"\n💾 Committed {fixes_applied} fixes to database")
        else:
            print(f"\n✅ No fixes needed")
    
    return fixes_applied

def test_all_armenia_teams():
    """Test all Armenia teams after fixes."""
    print("\n🧪 TESTING ALL ARMENIA TEAMS AFTER FIXES")
    print("=" * 50)
    
    with get_database() as db:
        armenia_teams = db.execute_query('''
            SELECT t.team_name, t2.team_name as canonical_name
            FROM teams t
            LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = "ARMENIA_PREMIER_LEAGUE"
            ORDER BY t.team_name
        ''')
        
        working_teams = 0
        total_teams = len(armenia_teams)
        
        print("Testing each team:")
        for _, team in armenia_teams.iterrows():
            team_name = team['team_name']
            canonical_name = team['canonical_name']
            
            # Test if team has stats
            stats = db.get_team_stats(team_name, "ARMENIA_PREMIER_LEAGUE")
            has_stats = not stats.empty
            
            if has_stats:
                working_teams += 1
                ppg = stats.iloc[0].get('points_per_game', 'N/A')
                status = f"✅ PPG: {ppg}"
            else:
                status = "❌ No stats"
            
            canonical_info = f" → {canonical_name}" if canonical_name != team_name else ""
            print(f"   {status} | {team_name}{canonical_info}")
        
        success_rate = (working_teams / total_teams) * 100
        print(f"\n📊 FINAL RESULTS:")
        print(f"   Working teams: {working_teams}/{total_teams}")
        print(f"   Success rate: {success_rate:.1f}%")
        
        return working_teams, total_teams

def test_specific_problem_teams():
    """Test the specific teams that were problematic."""
    print("\n🎯 TESTING SPECIFIC PROBLEM TEAMS")
    print("=" * 45)
    
    problem_teams = [
        "FC Avan Academy",
        "FC Banants", 
        "Ararat",
        "FC Artsakh"
    ]
    
    with get_database() as db:
        for team_name in problem_teams:
            print(f"\n🔍 Testing: {team_name}")
            
            # Test canonical resolution
            canonical = db._get_canonical_team_name(team_name, "ARMENIA_PREMIER_LEAGUE")
            print(f"   Canonical name: {canonical}")
            
            # Test team stats
            stats = db.get_team_stats(team_name, "ARMENIA_PREMIER_LEAGUE")
            if not stats.empty:
                ppg = stats.iloc[0].get('points_per_game', 'N/A')
                print(f"   ✅ Has stats: PPG = {ppg}")
            else:
                print(f"   ❌ No stats found")
            
            # Test team form
            form = db.get_team_form(team_name, "ARMENIA_PREMIER_LEAGUE", 3)
            print(f"   Form matches: {len(form)}")

def verify_web_access():
    """Verify that the problematic teams now work in web app."""
    print("\n🌐 WEB APPLICATION ACCESS TEST")
    print("=" * 40)
    
    problem_teams = [
        "FC Avan Academy",
        "FC Banants", 
        "Ararat",
        "FC Artsakh"
    ]
    
    print("These URLs should now work:")
    for team in problem_teams:
        encoded_team = team.replace(" ", "%20")
        url = f"http://localhost:5010/team/ARMENIA_PREMIER_LEAGUE/{encoded_team}"
        print(f"   {url}")

def main():
    """Main function."""
    print("🇦🇲 ARMENIA PREMIER LEAGUE SPECIFIC MAPPING FIXES")
    print("=" * 70)
    print("Fixing the exact canonical mapping issues identified in the report")
    
    # Apply specific fixes
    fixes_applied = apply_specific_armenia_fixes()
    
    # Test all teams
    working, total = test_all_armenia_teams()
    
    # Test specific problem teams
    test_specific_problem_teams()
    
    # Show web access info
    verify_web_access()
    
    # Final summary
    print(f"\n🎉 ARMENIA PREMIER LEAGUE FIX COMPLETE!")
    print(f"=" * 50)
    print(f"Applied {fixes_applied} specific canonical mapping fixes")
    print(f"Final success rate: {(working/total)*100:.1f}% ({working}/{total} teams)")
    
    if working == total:
        print("🎯 PERFECT! All Armenia Premier League teams now have stats!")
        print("✅ FC Avan Academy, FC Banants, Ararat, and FC Artsakh should all work now!")
    else:
        remaining = total - working
        print(f"⚠️  {remaining} teams still need attention")
        print("But the main config mapping issues should be resolved!")
    
    print(f"\n💡 Key Achievement:")
    print(f"Users can now search for historical team names and get current team data!")

if __name__ == "__main__":
    main()
