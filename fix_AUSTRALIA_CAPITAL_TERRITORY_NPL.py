#!/usr/bin/env python3
"""
Australia Capital Territory NPL Team Mapping Fix

Based on analysis from team_mapping_issues_report.md:
- 14/21 teams working (66.7% success rate)
- Has 8 config mappings available
- 3 canonical mapping issues
- 4 teams without stats

Target: Improve success rate to >90%
"""

import sys
import importlib.util
from typing import Dict, List, Tuple

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

# ============================================================================
# CONFIGURATION
# ============================================================================

LEAGUE_NAME = "AUSTRALIA_CAPITAL_TERRITORY_NPL"
LEAGUE_DISPLAY_NAME = "Australia Capital Territory NPL"

# Define specific mappings based on analysis
# Format: (old_team_name, target_team_name, reason)
SPECIFIC_MAPPINGS = [
    # Fix canonical mapping issues identified in report
    ("Canberra FC", "Canberra", "Remove FC suffix - canonical mapping issue"),
    ("O'Connor Knights FC", "O'Connor Knights", "Remove FC suffix - canonical mapping issue"),
    ("Tuggeranong United FC", "Tuggeranong United", "Remove FC suffix - canonical mapping issue"),
    
    # Additional mappings based on config patterns
    ("Canberra Olympic", "C. Olympic", "Use abbreviated form"),
    ("Gungahlin United FC", "Gungahlin", "Use shorter form"),
    ("Queanbeyan City SC", "Queanbeyan City", "Remove SC suffix"),
]

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

def load_league_config() -> Dict[str, str]:
    """Load the league config mappings if available."""
    config_path = f"src/scrapers/league_configs/{LEAGUE_NAME}.py"
    
    try:
        spec = importlib.util.spec_from_file_location("config", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        
        # Get the current config
        current_config = getattr(config_module, 'CURRENT_CONFIG', None)
        if current_config and 'TEAM_NAME_MAPPING' in current_config:
            return current_config['TEAM_NAME_MAPPING']
        
        # Fallback: try to get TEAM_NAME_MAPPING directly
        team_mapping = getattr(config_module, 'TEAM_NAME_MAPPING', None)
        if team_mapping:
            return team_mapping
            
        return {}
    except Exception as e:
        print(f"⚠️  No config file found or error loading: {e}")
        return {}

def analyze_current_state():
    """Analyze the current state of the league."""
    print(f"🔍 ANALYZING {LEAGUE_DISPLAY_NAME} CURRENT STATE")
    print("=" * 60)
    
    with get_database() as db:
        # Get all teams in the league
        teams = db.execute_query('''
            SELECT t.team_name, t.team_id, t.canonical_team_id, 
                   t2.team_name as canonical_name
            FROM teams t
            LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY t.team_name
        ''', (LEAGUE_NAME,))
        
        if teams.empty:
            print(f"❌ No teams found for {LEAGUE_NAME}")
            return {}
        
        print(f"📊 Found {len(teams)} teams in {LEAGUE_DISPLAY_NAME}")
        
        # Check which teams have stats
        teams_info = {}
        working_teams = 0
        teams_with_stats = []
        teams_without_stats = []
        
        for _, team in teams.iterrows():
            team_name = team['team_name']
            team_id = team['team_id']
            canonical_name = team['canonical_name']
            
            # Check if team has stats via get_team_stats (uses canonical resolution)
            stats = db.get_team_stats(team_name, LEAGUE_NAME)
            has_stats = not stats.empty
            
            if has_stats:
                working_teams += 1
                teams_with_stats.append(team_name)
            else:
                teams_without_stats.append(team_name)
            
            teams_info[team_name] = {
                'team_id': team_id,
                'canonical_name': canonical_name,
                'has_stats': has_stats,
                'canonical_id': team['canonical_team_id']
            }
            
            status = "✅" if has_stats else "❌"
            canonical_info = f" → {canonical_name}" if canonical_name != team_name else ""
            print(f"   {status} {team_name}{canonical_info}")
        
        success_rate = (working_teams / len(teams)) * 100
        print(f"\n📊 Current success rate: {working_teams}/{len(teams)} ({success_rate:.1f}%)")
        
        if teams_without_stats:
            print(f"\n❌ Teams without stats: {teams_without_stats}")
        
        return teams_info

def apply_config_mappings(config_mappings: Dict[str, str]):
    """Apply config mappings if available."""
    if not config_mappings:
        print("⚠️  No config mappings to apply")
        return 0
    
    print(f"\n🔧 APPLYING CONFIG MAPPINGS")
    print("=" * 50)
    print(f"Found {len(config_mappings)} config mappings:")
    for current, old in config_mappings.items():
        print(f"   {current} ← {old}")
    
    fixes_applied = 0
    
    with get_database() as db:
        for current_team, old_team in config_mappings.items():
            print(f"\n📋 Processing: {current_team} ← {old_team}")
            
            # Get team IDs
            current_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (current_team, LEAGUE_NAME))
            
            old_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (old_team, LEAGUE_NAME))
            
            if not current_id:
                print(f"   ⚠️  Current team '{current_team}' not found")
                continue
            
            if not old_id:
                print(f"   ⚠️  Old team '{old_team}' not found")
                continue
            
            # Check if current team has stats
            current_stats = db.get_team_stats(current_team, LEAGUE_NAME)
            old_stats = db.get_team_stats(old_team, LEAGUE_NAME)
            
            if current_stats.empty and old_stats.empty:
                print(f"   ⚠️  Neither team has stats - skipping")
                continue
            
            # Determine mapping direction
            if not current_stats.empty and old_stats.empty:
                # Standard mapping: old → current
                target_id, source_id = current_id, old_id
                direction = f"{old_team} → {current_team}"
            elif current_stats.empty and not old_stats.empty:
                # Reverse mapping: current → old
                target_id, source_id = old_id, current_id
                direction = f"{current_team} → {old_team}"
            else:
                print(f"   ⚠️  Both teams have stats - skipping to avoid conflicts")
                continue
            
            try:
                db.conn.execute(
                    'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                    (target_id, source_id)
                )
                print(f"   ✅ Applied mapping: {direction}")
                fixes_applied += 1
            except Exception as e:
                print(f"   ❌ Error applying mapping: {e}")
        
        if fixes_applied > 0:
            db.conn.commit()
            print(f"\n💾 Committed {fixes_applied} config-based fixes")
    
    return fixes_applied

def apply_specific_mappings():
    """Apply the specific mappings defined in SPECIFIC_MAPPINGS."""
    if not SPECIFIC_MAPPINGS:
        print("⚠️  No specific mappings to apply")
        return 0
    
    print(f"\n🎯 APPLYING SPECIFIC MAPPINGS")
    print("=" * 45)
    
    fixes_applied = 0
    
    with get_database() as db:
        for old_team, target_team, reason in SPECIFIC_MAPPINGS:
            print(f"\n🔧 Fixing: {old_team} → {target_team}")
            print(f"   Reason: {reason}")
            
            # Get team IDs
            old_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (old_team, LEAGUE_NAME))
            
            target_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (target_team, LEAGUE_NAME))
            
            if not old_id:
                print(f"   ❌ Old team '{old_team}' not found")
                continue
            
            if not target_id:
                print(f"   ❌ Target team '{target_team}' not found")
                continue
            
            # Verify target has stats
            target_stats = db.get_team_stats(target_team, LEAGUE_NAME)
            if target_stats.empty:
                print(f"   ⚠️  Target team '{target_team}' has no stats - skipping")
                continue
            
            try:
                db.conn.execute(
                    'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                    (target_id, old_id)
                )
                print(f"   ✅ Applied mapping")
                fixes_applied += 1
            except Exception as e:
                print(f"   ❌ Error applying mapping: {e}")
        
        if fixes_applied > 0:
            db.conn.commit()
            print(f"\n💾 Committed {fixes_applied} specific fixes")
    
    return fixes_applied

def fix_circular_references():
    """Fix teams with stats that point to teams without stats (critical issue)."""
    print(f"\n🚨 CHECKING FOR CIRCULAR REFERENCES")
    print("=" * 50)

    fixes_applied = 0

    with get_database() as db:
        # Find teams with stats that don't point to themselves
        problematic_teams = db.execute_query('''
            SELECT t.team_id, t.team_name, t.canonical_team_id,
                   COUNT(ts.team_id) as stat_count
            FROM teams t
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ? AND t.team_id != t.canonical_team_id
            GROUP BY t.team_id, t.team_name, t.canonical_team_id
            HAVING stat_count > 0
        ''', (LEAGUE_NAME,))

        if problematic_teams.empty:
            print("✅ No circular references found")
            return 0

        print(f"Found {len(problematic_teams)} teams with stats pointing to other teams:")

        for _, team in problematic_teams.iterrows():
            team_id = team['team_id']
            team_name = team['team_name']
            canonical_id = team['canonical_team_id']
            stat_count = team['stat_count']

            print(f"\n🔧 Fixing: {team_name} (ID: {team_id})")
            print(f"   Has {stat_count} stats but points to ID: {canonical_id}")
            print(f"   Making it canonical (point to itself)")

            # Make the team canonical (point to itself)
            db.conn.execute(
                'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                (team_id, team_id)
            )
            fixes_applied += 1

        if fixes_applied > 0:
            db.conn.commit()
            print(f"\n💾 Committed {fixes_applied} circular reference fixes")

    return fixes_applied

def verify_final_state():
    """Verify the final state after all fixes."""
    print(f"\n📊 FINAL {LEAGUE_DISPLAY_NAME} STATUS")
    print("=" * 50)
    
    with get_database() as db:
        teams = db.execute_query('''
            SELECT t.team_name, t2.team_name as canonical_name
            FROM teams t
            LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY t.team_name
        ''', (LEAGUE_NAME,))
        
        working_teams = 0
        total_teams = len(teams)
        
        for _, team in teams.iterrows():
            team_name = team['team_name']
            canonical_name = team['canonical_name']
            
            # Test if team has stats
            stats = db.get_team_stats(team_name, LEAGUE_NAME)
            has_stats = not stats.empty
            
            if has_stats:
                working_teams += 1
            
            status = "✅" if has_stats else "❌"
            canonical_info = f" → {canonical_name}" if canonical_name != team_name else ""
            print(f"   {status} {team_name}{canonical_info}")
        
        success_rate = (working_teams / total_teams) * 100
        print(f"\n📈 Final Results:")
        print(f"   Working teams: {working_teams}/{total_teams}")
        print(f"   Success rate: {success_rate:.1f}%")
        
        return working_teams, total_teams

def main():
    """Main function."""
    print(f"🏆 {LEAGUE_DISPLAY_NAME.upper()} TEAM MAPPING FIX")
    print("=" * 70)
    print(f"Fixing canonical team mappings for {LEAGUE_DISPLAY_NAME}")
    
    # Analyze current state
    teams_info = analyze_current_state()
    if not teams_info:
        return
    
    # Load and apply config mappings
    config_mappings = load_league_config()
    config_fixes = apply_config_mappings(config_mappings)
    
    # Apply specific mappings
    specific_fixes = apply_specific_mappings()

    # 🚨 CRITICAL: Fix circular references (teams with stats pointing to teams without stats)
    circular_fixes = fix_circular_references()

    # Verify final state
    working, total = verify_final_state()
    
    # Summary
    total_fixes = config_fixes + specific_fixes + circular_fixes
    print(f"\n🎉 {LEAGUE_DISPLAY_NAME.upper()} FIX COMPLETE!")
    print(f"Applied {total_fixes} canonical mapping fixes")
    if circular_fixes > 0:
        print(f"🚨 Fixed {circular_fixes} critical circular references!")
    print(f"Final success rate: {(working/total)*100:.1f}% ({working}/{total} teams)")
    
    if total_fixes > 0:
        print(f"🎯 Applied {total_fixes} fixes!")
    
    if working == total:
        print(f"🎯 Perfect! All {LEAGUE_DISPLAY_NAME} teams now have stats!")
    else:
        remaining = total - working
        print(f"⚠️  {remaining} teams still need attention")

if __name__ == "__main__":
    main()