#!/usr/bin/env python3
"""
Comprehensive Team Canonical Mapping Fix

This script identifies and fixes ALL canonical team mapping issues across ALL leagues
by analyzing team names, finding duplicates/variations, and creating proper canonical
mappings based on which teams have the most data (stats, H2H, matches).
"""

import sys
import sqlite3
import re
from collections import defaultdict
from typing import Dict, List, Tuple, Set

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

def normalize_team_name_for_comparison(name: str) -> str:
    """Normalize team name for comparison to find similar teams."""
    # Remove common prefixes/suffixes and normalize
    name = name.lower().strip()
    
    # Remove common football club indicators
    removals = [
        r'\bfc\b', r'\bcf\b', r'\bclub\b', r'\bde\b', r'\bfútbol\b', r'\bfootball\b',
        r'\breal\b', r'\brcd\b', r'\bcd\b', r'\bud\b', r'\bac\b', r'\bsc\b',
        r'\bdeportivo\b', r'\breial\b', r'\bclube\b'
    ]
    
    for removal in removals:
        name = re.sub(removal, '', name)
    
    # Remove special characters and extra spaces
    name = re.sub(r'[^\w\s]', '', name)
    name = re.sub(r'\s+', ' ', name).strip()
    
    return name

def find_team_groups_by_similarity(teams_data: List[Dict]) -> Dict[str, List[Dict]]:
    """Group teams by similarity to find potential duplicates."""
    groups = defaultdict(list)
    
    for team in teams_data:
        normalized = normalize_team_name_for_comparison(team['team_name'])
        groups[normalized].append(team)
    
    # Only return groups with multiple teams (potential duplicates)
    return {k: v for k, v in groups.items() if len(v) > 1}

def get_team_data_score(team_id: int, db) -> int:
    """Calculate a score for how much data a team has (higher = more canonical)."""
    score = 0
    
    # Check team stats
    stats = db.execute_query("SELECT COUNT(*) as count FROM team_stats WHERE team_id = ?", (team_id,))
    if not stats.empty:
        score += stats.iloc[0]['count'] * 10
    
    # Check match results (as home team)
    home_matches = db.execute_query("SELECT COUNT(*) as count FROM match_results WHERE home_team_id = ?", (team_id,))
    if not home_matches.empty:
        score += home_matches.iloc[0]['count'] * 2
    
    # Check match results (as away team)
    away_matches = db.execute_query("SELECT COUNT(*) as count FROM match_results WHERE away_team_id = ?", (team_id,))
    if not away_matches.empty:
        score += away_matches.iloc[0]['count'] * 2
    
    # Check head-to-head stats (as home team)
    h2h_home = db.execute_query("SELECT COUNT(*) as count FROM head_to_head_stats WHERE home_team_id = ?", (team_id,))
    if not h2h_home.empty:
        score += h2h_home.iloc[0]['count'] * 5
    
    # Check head-to-head stats (as away team)
    h2h_away = db.execute_query("SELECT COUNT(*) as count FROM head_to_head_stats WHERE away_team_id = ?", (team_id,))
    if not h2h_away.empty:
        score += h2h_away.iloc[0]['count'] * 5
    
    return score

def choose_canonical_team(team_group: List[Dict], db) -> Dict:
    """Choose the best canonical team from a group based on data availability."""
    best_team = None
    best_score = -1
    
    for team in team_group:
        score = get_team_data_score(team['team_id'], db)
        team['data_score'] = score
        
        if score > best_score:
            best_score = score
            best_team = team
    
    return best_team

def fix_all_canonical_mappings():
    """Fix all canonical mappings across all leagues."""
    print("🔧 COMPREHENSIVE TEAM CANONICAL MAPPING FIX")
    print("=" * 60)
    
    with get_database() as db:
        # Get all teams across all leagues
        all_teams = db.execute_query('''
            SELECT t.team_id, t.team_name, t.canonical_team_id, l.league_name
            FROM teams t
            JOIN leagues l ON t.league_id = l.league_id
            ORDER BY l.league_name, t.team_name
        ''')
        
        if all_teams.empty:
            print("❌ No teams found in database")
            return
        
        print(f"📊 Found {len(all_teams)} teams across all leagues")
        
        # Group by league for processing
        leagues = all_teams['league_name'].unique()
        total_fixes = 0
        
        for league in leagues:
            print(f"\n🏆 Processing {league}")
            print("-" * 40)
            
            league_teams = all_teams[all_teams['league_name'] == league].to_dict('records')
            
            # Find similar team groups
            similar_groups = find_team_groups_by_similarity(league_teams)
            
            if not similar_groups:
                print(f"   ✅ No duplicate teams found in {league}")
                continue
            
            print(f"   🔍 Found {len(similar_groups)} groups with potential duplicates")
            
            league_fixes = 0
            
            for normalized_name, team_group in similar_groups.items():
                if len(team_group) < 2:
                    continue
                
                print(f"\n   📋 Group: {normalized_name}")
                for team in team_group:
                    print(f"      - {team['team_name']} (id: {team['team_id']})")
                
                # Choose canonical team
                canonical_team = choose_canonical_team(team_group, db)
                
                print(f"   🎯 Chosen canonical: {canonical_team['team_name']} (score: {canonical_team['data_score']})")
                
                # Update all other teams to point to canonical
                for team in team_group:
                    if team['team_id'] != canonical_team['team_id']:
                        current_canonical = team['canonical_team_id']
                        new_canonical = canonical_team['team_id']
                        
                        if current_canonical != new_canonical:
                            try:
                                db.conn.execute(
                                    "UPDATE teams SET canonical_team_id = ? WHERE team_id = ?",
                                    (new_canonical, team['team_id'])
                                )
                                print(f"      ✅ Updated {team['team_name']} -> {canonical_team['team_name']}")
                                league_fixes += 1
                                total_fixes += 1
                            except Exception as e:
                                print(f"      ❌ Error updating {team['team_name']}: {e}")
                        else:
                            print(f"      ✅ {team['team_name']} already correctly mapped")
            
            if league_fixes > 0:
                db.conn.commit()
                print(f"   💾 Committed {league_fixes} fixes for {league}")
            else:
                print(f"   ✅ No fixes needed for {league}")
        
        print(f"\n🎉 TOTAL FIXES APPLIED: {total_fixes}")
        
        return total_fixes

def test_random_h2h_queries():
    """Test random H2H queries to verify fixes work."""
    print("\n🧪 TESTING RANDOM H2H QUERIES")
    print("=" * 40)
    
    # Test cases: (team1, team2, league)
    test_cases = [
        ("Alaves", "Athletic Bilbao", "SPAIN_LA_LIGA"),
        ("Brighton", "Brighton & Hove Albion FC", "ENGLAND_PREMIER_LEAGUE"),
        ("Tottenham", "Tottenham Hotspur", "ENGLAND_PREMIER_LEAGUE"),
        ("Bournemouth", "AFC Bournemouth", "ENGLAND_PREMIER_LEAGUE"),
        ("Getafe", "Getafe Club de Fútbol", "SPAIN_LA_LIGA"),
        ("RCD Mallorca", "Mallorca", "SPAIN_LA_LIGA"),
    ]
    
    with get_database() as db:
        working_count = 0
        
        for team1, team2, league in test_cases:
            # Check if both teams exist
            teams = db.get_teams(league)
            if teams.empty:
                continue
                
            team_names = teams['team_name'].tolist()
            
            if team1 in team_names and team2 in team_names:
                h2h = db.get_head_to_head_stats(team1, team2, league)
                if not h2h.empty:
                    print(f"✅ {team1} vs {team2}: {h2h.iloc[0]['total_matches']} matches")
                    working_count += 1
                else:
                    print(f"❌ {team1} vs {team2}: No data")
            elif team1 in team_names or team2 in team_names:
                # One team exists, check if it's now canonical
                existing = team1 if team1 in team_names else team2
                missing = team2 if team1 in team_names else team1
                print(f"ℹ️  {team1} vs {team2}: Only {existing} found (expected if {missing} was merged)")
            else:
                print(f"⚠️  {team1} vs {team2}: Neither team found in {league}")
        
        print(f"\n📊 Working H2H queries: {working_count}/{len(test_cases)}")

def show_final_summary():
    """Show final summary of team counts per league."""
    print("\n📈 FINAL SUMMARY")
    print("=" * 30)
    
    with get_database() as db:
        # Show team counts per league after fixes
        league_summary = db.execute_query('''
            SELECT l.league_name, COUNT(DISTINCT t.canonical_team_id) as unique_teams, COUNT(t.team_id) as total_teams
            FROM teams t
            JOIN leagues l ON t.league_id = l.league_id
            GROUP BY l.league_name
            ORDER BY l.league_name
        ''')
        
        if not league_summary.empty:
            print("Teams per league (unique/total):")
            for _, row in league_summary.iterrows():
                league = row['league_name']
                unique = row['unique_teams']
                total = row['total_teams']
                duplicates = total - unique
                print(f"  {league}: {unique} unique teams ({duplicates} duplicates resolved)")

def main():
    """Main function."""
    print("🌍 GLOBAL TEAM MAPPING FIX")
    print("=" * 50)
    print("This will fix ALL canonical team mappings across ALL leagues")
    print("by analyzing team similarities and data availability.")
    
    # Apply fixes
    total_fixes = fix_all_canonical_mappings()
    
    if total_fixes > 0:
        # Test some H2H queries
        test_random_h2h_queries()
        
        # Show final summary
        show_final_summary()
        
        print(f"\n🎉 SUCCESS!")
        print(f"Fixed {total_fixes} canonical team mappings across all leagues.")
        print("Head-to-head functionality should now work much better!")
    else:
        print("\n✅ No fixes were needed - all mappings were already correct!")

if __name__ == "__main__":
    main()
