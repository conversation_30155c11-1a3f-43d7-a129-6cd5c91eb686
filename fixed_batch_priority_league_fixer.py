#!/usr/bin/env python3
"""
Fixed Batch Priority League Fixer

Systematically fixes the top priority leagues by analyzing each one and applying targeted fixes.
Uses the same approach that worked for AUSTRIA_2_LIGA.
"""

import sys
from typing import Dict, List, Tuple, Optional

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

def analyze_league(league_name: str) -> Tuple[List[str], List[str], float]:
    """Analyze a league and return broken teams, working teams, and success rate."""
    with get_database() as db:
        teams = db.execute_query('''
            SELECT t.team_name FROM teams t
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY t.team_name
        ''', (league_name,))
        
        if teams.empty:
            return [], [], 0.0
        
        working_teams = []
        broken_teams = []
        
        for _, team in teams.iterrows():
            stats = db.get_team_stats(team['team_name'], league_name)
            if not stats.empty:
                working_teams.append(team['team_name'])
            else:
                broken_teams.append(team['team_name'])
        
        total_teams = len(teams)
        success_rate = (len(working_teams) / total_teams) * 100 if total_teams > 0 else 0
        
        return broken_teams, working_teams, success_rate

def find_best_mapping(broken_team: str, working_teams: List[str]) -> Optional[str]:
    """Find the best mapping candidate for a broken team."""
    broken_lower = broken_team.lower()
    
    # Remove common suffixes and prefixes for comparison
    def clean_name(name):
        name = name.lower()
        # Remove common suffixes
        for suffix in [' fc', ' sc', ' sv', ' cf', ' ac', ' united fc', ' city fc']:
            if name.endswith(suffix):
                name = name[:-len(suffix)]
                break
        # Remove common prefixes  
        for prefix in ['fc ', 'sc ', 'sv ', 'cf ', 'ac ']:
            if name.startswith(prefix):
                name = name[len(prefix):]
                break
        return name.strip()
    
    broken_clean = clean_name(broken_team)
    
    # Look for exact matches after cleaning
    for working_team in working_teams:
        working_clean = clean_name(working_team)
        if broken_clean == working_clean:
            return working_team
    
    # Look for partial matches
    for working_team in working_teams:
        working_clean = clean_name(working_team)
        if broken_clean in working_clean or working_clean in broken_clean:
            # Make sure it's a substantial match (not just 1-2 characters)
            if len(broken_clean) > 3 and len(working_clean) > 3:
                return working_team
    
    return None

def apply_canonical_mapping(old_team: str, target_team: str, league_name: str) -> bool:
    """Apply a canonical mapping between two teams using the same method as AUSTRIA_2_LIGA fix."""
    with get_database() as db:
        try:
            # Get team IDs
            old_team_query = '''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            '''
            
            target_team_query = '''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            '''
            
            old_team_result = db.execute_query(old_team_query, (old_team, league_name))
            target_team_result = db.execute_query(target_team_query, (target_team, league_name))
            
            if old_team_result is None or old_team_result.empty:
                print(f"   ❌ Old team '{old_team}' not found")
                return False
                
            if target_team_result is None or target_team_result.empty:
                print(f"   ❌ Target team '{target_team}' not found")
                return False
            
            old_team_id = old_team_result.iloc[0]['team_id']
            target_team_id = target_team_result.iloc[0]['team_id']
            
            # Check if target team has stats (sanity check)
            target_stats = db.get_team_stats(target_team, league_name)
            if target_stats.empty:
                print(f"   ⚠️  Target team '{target_team}' has no stats - skipping")
                return False
            
            # Apply the mapping using direct SQL
            update_query = '''
                UPDATE teams 
                SET canonical_team_id = ? 
                WHERE team_id = ?
            '''
            
            # Execute the update
            db.execute_query(update_query, (target_team_id, old_team_id))
            
            # Verify the mapping was applied
            verify_query = '''
                SELECT canonical_team_id FROM teams WHERE team_id = ?
            '''
            verify_result = db.execute_query(verify_query, (old_team_id,))
            
            if not verify_result.empty and verify_result.iloc[0]['canonical_team_id'] == target_team_id:
                return True
            else:
                print(f"   ❌ Mapping verification failed")
                return False
            
        except Exception as e:
            print(f"   ❌ Error applying mapping: {e}")
            return False

def fix_league(league_name: str) -> Tuple[bool, float, float]:
    """Fix a single league and return (success, before_rate, after_rate)."""
    print(f"\n{'='*70}")
    print(f"🎯 FIXING {league_name}")
    print(f"{'='*70}")
    
    # Analyze current state
    broken_teams, working_teams, before_rate = analyze_league(league_name)
    total_teams = len(broken_teams) + len(working_teams)
    
    print(f"📊 Before: {len(working_teams)}/{total_teams} ({before_rate:.1f}%)")
    
    if before_rate >= 90:
        print(f"✅ Already above 90% - skipping")
        return True, before_rate, before_rate
    
    if not broken_teams:
        print(f"✅ No broken teams found")
        return True, before_rate, before_rate
    
    print(f"🔧 Fixing {len(broken_teams)} broken teams...")
    print(f"❌ Broken teams: {broken_teams}")
    
    fixes_applied = 0
    
    for broken_team in broken_teams:
        print(f"\n🔍 Analyzing: {broken_team}")
        target_team = find_best_mapping(broken_team, working_teams)
        
        if not target_team:
            print(f"   ⚠️  No suitable mapping found")
            continue
        
        print(f"   🎯 Mapping to: {target_team}")
        
        if apply_canonical_mapping(broken_team, target_team, league_name):
            print(f"   ✅ Applied mapping successfully")
            fixes_applied += 1
        else:
            print(f"   ❌ Failed to apply mapping")
    
    # Check final state
    _, _, after_rate = analyze_league(league_name)
    
    print(f"\n📈 Results:")
    print(f"   Before: {before_rate:.1f}%")
    print(f"   After:  {after_rate:.1f}%")
    print(f"   Improvement: {after_rate - before_rate:.1f}%")
    print(f"   Fixes applied: {fixes_applied}")
    
    success = after_rate >= 90
    if success:
        print(f"🎉 {league_name} now above 90%!")
    
    return success, before_rate, after_rate

def main():
    """Main function to fix the next 10 priority leagues."""
    print("🎯 FIXED BATCH PRIORITY LEAGUE FIXER")
    print("=" * 70)
    print("Testing with next 10 highest priority leagues")
    
    # Next 10 priority leagues after AUSTRIA_2_LIGA (which we already fixed)
    # These are the leagues with 80-89% success rate
    priority_leagues = [
        "INDIA_SUPER_LEAGUE",           # 89.7%
        "BAHRAIN_PREMIER_LEAGUE",       # 89.3%
        "ENGLAND_ISTHMIAN_LEAGUE",      # 88.6%
        "SPAIN_SEGUNDA_DIVISION",       # 88.4%
        "SCOTLAND_CHAMPIONSHIP",        # 87.5%
        "ENGLAND_PREMIER_LEAGUE",       # 87.2%
        "SWITZERLAND_CHALLENGE_LEAGUE", # 87.0%
        "CZECH_REPUBLIC_FIRST_LEAGUE",  # 86.1%
        "SCOTLAND_PREMIERSHIP",         # 85.7%
        "BRAZIL_CATARINENSE",           # 85.4%
    ]
    
    print(f"\n🎯 PROCESSING {len(priority_leagues)} PRIORITY LEAGUES")
    print("=" * 60)
    
    results = []
    leagues_fixed = 0
    total_improvement = 0
    
    for league_name in priority_leagues:
        success, before_rate, after_rate = fix_league(league_name)
        
        improvement = after_rate - before_rate
        total_improvement += improvement
        
        if success:
            leagues_fixed += 1
        
        results.append({
            'league': league_name,
            'success': success,
            'before_rate': before_rate,
            'after_rate': after_rate,
            'improvement': improvement
        })
    
    # Final summary
    print(f"\n{'='*70}")
    print(f"📊 BATCH PRIORITY FIXES SUMMARY")
    print(f"{'='*70}")
    print(f"Leagues processed: {len(priority_leagues)}")
    print(f"Leagues reaching 90%+: {leagues_fixed}")
    print(f"Average improvement: {total_improvement / len(priority_leagues):.1f}%")
    
    print(f"\n📈 DETAILED RESULTS:")
    print("-" * 70)
    
    for result in results:
        status = "🎉 FIXED" if result['success'] else "⚠️  NEEDS MORE WORK"
        improvement_str = f"(+{result['improvement']:.1f}%)" if result['improvement'] > 0 else f"({result['improvement']:.1f}%)" if result['improvement'] < 0 else "(no change)"
        
        print(f"{status} {result['league']}: {result['after_rate']:.1f}% {improvement_str}")
    
    print(f"\n🚀 NEXT STEPS:")
    remaining_leagues = [r for r in results if not r['success']]
    if remaining_leagues:
        print(f"   • {len(remaining_leagues)} leagues still need manual attention")
        print(f"   • Consider applying AUSTRIA_2_LIGA approach one by one")
    else:
        print(f"   • All priority leagues now above 90%!")
        print(f"   • Move to next tier of leagues")

if __name__ == "__main__":
    main()