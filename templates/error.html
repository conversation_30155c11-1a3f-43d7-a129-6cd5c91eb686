{% extends "base.html" %}

{% block title %}Error{% endblock %}

{% block extra_css %}
<style>
    .error-container {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
        border-radius: 15px;
        padding: 3rem;
        margin: 2rem 0;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    
    .error-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.8;
    }
    
    .error-message {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        font-family: monospace;
        font-size: 0.9rem;
        text-align: left;
        word-break: break-word;
    }
    
    .suggestions {
        background: white;
        color: #333;
        border-radius: 10px;
        padding: 2rem;
        margin: 2rem 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .suggestion-item {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #eee;
    }
    
    .suggestion-item:last-child {
        border-bottom: none;
    }
    
    .suggestion-icon {
        color: #667eea;
        margin-right: 1rem;
        font-size: 1.2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item active">Error</li>
        </ol>
    </nav>

    <!-- Error Display -->
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <h1>Oops! Something went wrong</h1>
        <p class="lead">We encountered an unexpected error while processing your request.</p>
        
        {% if error %}
        <div class="error-message">
            <strong>Error Details:</strong><br>
            {{ error }}
        </div>
        {% endif %}
    </div>

    <!-- Suggestions -->
    <div class="suggestions">
        <h3><i class="fas fa-lightbulb me-2"></i>What you can do</h3>
        
        <div class="suggestion-item">
            <div class="suggestion-icon">
                <i class="fas fa-refresh"></i>
            </div>
            <div>
                <strong>Try Again</strong><br>
                <span class="text-muted">This might be a temporary issue. Try refreshing the page or going back and trying again.</span>
            </div>
        </div>
        
        <div class="suggestion-item">
            <div class="suggestion-icon">
                <i class="fas fa-home"></i>
            </div>
            <div>
                <strong>Go Home</strong><br>
                <span class="text-muted">Return to the main page and navigate to what you're looking for.</span>
            </div>
        </div>
        
        <div class="suggestion-item">
            <div class="suggestion-icon">
                <i class="fas fa-search"></i>
            </div>
            <div>
                <strong>Search</strong><br>
                <span class="text-muted">Use the search function to find teams, leagues, or specific information.</span>
            </div>
        </div>
        
        <div class="suggestion-item">
            <div class="suggestion-icon">
                <i class="fas fa-list"></i>
            </div>
            <div>
                <strong>Browse Leagues</strong><br>
                <span class="text-muted">Check out the available leagues and teams in our database.</span>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row">
        <div class="col-md-12 text-center">
            <a href="javascript:history.back()" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Go Back
            </a>
            <a href="/" class="btn btn-primary me-2">
                <i class="fas fa-home me-2"></i>Home
            </a>
            <a href="/leagues" class="btn btn-outline-primary">
                <i class="fas fa-list me-2"></i>Browse Leagues
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh button for temporary errors
    const errorMessage = '{{ error|lower }}';
    if (errorMessage.includes('timeout') || errorMessage.includes('connection') || errorMessage.includes('temporary')) {
        const refreshButton = document.createElement('button');
        refreshButton.className = 'btn btn-outline-light mt-3';
        refreshButton.innerHTML = '<i class="fas fa-refresh me-2"></i>Refresh Page';
        refreshButton.onclick = () => window.location.reload();
        
        document.querySelector('.error-container').appendChild(refreshButton);
    }
});
</script>
{% endblock %}
