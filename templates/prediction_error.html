{% extends "base.html" %}

{% block title %}Prediction Error{% endblock %}

{% block extra_css %}
<style>
    .error-container {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
        border-radius: 15px;
        padding: 3rem;
        margin: 2rem 0;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    
    .error-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.8;
    }
    
    .error-message {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        font-family: monospace;
        font-size: 0.9rem;
        text-align: left;
    }
    
    .suggestions {
        background: white;
        color: #333;
        border-radius: 10px;
        padding: 2rem;
        margin: 2rem 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .suggestion-item {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #eee;
    }
    
    .suggestion-item:last-child {
        border-bottom: none;
    }
    
    .suggestion-icon {
        color: #667eea;
        margin-right: 1rem;
        font-size: 1.2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            {% if league_name %}
            <li class="breadcrumb-item"><a href="/league/{{ league_name }}">{{ league_name.replace('_', ' ').title() }}</a></li>
            <li class="breadcrumb-item"><a href="/predictions/{{ league_name }}">Predictions</a></li>
            {% endif %}
            <li class="breadcrumb-item active">Error</li>
        </ol>
    </nav>

    <!-- Error Display -->
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <h1>Prediction Error</h1>
        
        {% if team1 and team2 %}
        <p class="lead">
            Unable to predict match: <strong>{{ team1 }} vs {{ team2 }}</strong>
        </p>
        {% endif %}
        
        {% if league_name %}
        <p>League: {{ league_name.replace('_', ' ').title() }}</p>
        {% endif %}
        
        {% if error %}
        <div class="error-message">
            <strong>Error Details:</strong><br>
            {{ error }}
        </div>
        {% endif %}
    </div>

    <!-- Suggestions -->
    <div class="suggestions">
        <h3><i class="fas fa-lightbulb me-2"></i>Possible Solutions</h3>
        
        <div class="suggestion-item">
            <div class="suggestion-icon">
                <i class="fas fa-search"></i>
            </div>
            <div>
                <strong>Check Team Names</strong><br>
                <span class="text-muted">Ensure both team names are spelled correctly and exist in the selected league.</span>
            </div>
        </div>
        
        <div class="suggestion-item">
            <div class="suggestion-icon">
                <i class="fas fa-database"></i>
            </div>
            <div>
                <strong>Verify Data Availability</strong><br>
                <span class="text-muted">The teams might not have sufficient statistical data for accurate predictions.</span>
            </div>
        </div>
        
        <div class="suggestion-item">
            <div class="suggestion-icon">
                <i class="fas fa-cogs"></i>
            </div>
            <div>
                <strong>Model Training</strong><br>
                <span class="text-muted">The prediction models might need to be retrained for this league.</span>
            </div>
        </div>
        
        <div class="suggestion-item">
            <div class="suggestion-icon">
                <i class="fas fa-refresh"></i>
            </div>
            <div>
                <strong>Try Again</strong><br>
                <span class="text-muted">This might be a temporary issue. Try refreshing the page or selecting different teams.</span>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row">
        <div class="col-md-12 text-center">
            {% if league_name %}
            <a href="/predictions/{{ league_name }}" class="btn btn-primary me-2">
                <i class="fas fa-arrow-left me-2"></i>Try Different Teams
            </a>
            <a href="/league/{{ league_name }}" class="btn btn-secondary me-2">
                <i class="fas fa-table me-2"></i>Back to League
            </a>
            {% endif %}
            <a href="/leagues" class="btn btn-outline-primary">
                <i class="fas fa-list me-2"></i>All Leagues
            </a>
        </div>
    </div>

    <!-- Debug Information (only in development) -->
    {% if config.DEBUG %}
    <div class="mt-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bug me-2"></i>Debug Information</h5>
            </div>
            <div class="card-body">
                <p><strong>League:</strong> {{ league_name or 'Not specified' }}</p>
                <p><strong>Home Team:</strong> {{ team1 or 'Not specified' }}</p>
                <p><strong>Away Team:</strong> {{ team2 or 'Not specified' }}</p>
                <p><strong>Error:</strong> {{ error or 'No error message provided' }}</p>
                <p><strong>Timestamp:</strong> {{ moment().format('YYYY-MM-DD HH:mm:ss') }}</p>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh suggestion (optional)
    const refreshButton = document.createElement('button');
    refreshButton.className = 'btn btn-outline-secondary mt-3';
    refreshButton.innerHTML = '<i class="fas fa-refresh me-2"></i>Refresh Page';
    refreshButton.onclick = () => window.location.reload();
    
    // Add refresh button to suggestions if error seems temporary
    const errorMessage = '{{ error|lower }}';
    if (errorMessage.includes('timeout') || errorMessage.includes('connection') || errorMessage.includes('temporary')) {
        document.querySelector('.suggestions').appendChild(refreshButton);
    }
});
</script>
{% endblock %}
