{% extends "base.html" %}

{% block title %}{{ team_name }} - {{ league_name.replace('_', ' ').title() }}{% endblock %}

{% block content %}
<!-- Team Header -->
<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('leagues') }}">Leagues</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('league_detail', league_name=league_name) }}">{{ league_name.replace('_', ' ').title() }}</a></li>
                <li class="breadcrumb-item active">{{ team_name }}</li>
            </ol>
        </nav>
        
        <h1><i class="fas fa-users me-2"></i>{{ team_name }}</h1>
        <p class="lead text-muted">{{ league_name.replace('_', ' ').title() }}</p>
    </div>
</div>

<!-- Team Statistics Cards -->
{% if stats %}
<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-futbol fa-2x mb-2"></i>
                <h4>{{ stats.total_played }}</h4>
                <p class="card-text">Matches Played</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-trophy fa-2x mb-2"></i>
                <h4>{{ "%.2f"|format(stats.points_per_game) }}</h4>
                <p class="card-text">Points/Game</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-bullseye fa-2x mb-2"></i>
                <h4>{{ "%.2f"|format(stats.goals_scored_per_match_all) }}</h4>
                <p class="card-text">Goals/Match</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <i class="fas fa-shield-alt fa-2x mb-2"></i>
                <h4>{{ "%.2f"|format(stats.goals_conceded_per_match_all) }}</h4>
                <p class="card-text">Conceded/Match</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Main Content Tabs -->
<ul class="nav nav-tabs" id="teamTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats-content" type="button">
            <i class="fas fa-chart-bar me-2"></i>Statistics
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="form-tab" data-bs-toggle="tab" data-bs-target="#form-content" type="button">
            <i class="fas fa-chart-line me-2"></i>Recent Form
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="predictions-tab" data-bs-toggle="tab" data-bs-target="#predictions-content" type="button">
            <i class="fas fa-crystal-ball me-2"></i>Predictions
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="h2h-tab" data-bs-toggle="tab" data-bs-target="#h2h-content" type="button">
            <i class="fas fa-balance-scale me-2"></i>Head-to-Head
        </button>
    </li>
</ul>

<div class="tab-content" id="teamTabsContent">
    <!-- Statistics Tab -->
    <div class="tab-pane fade show active" id="stats-content" role="tabpanel">
        {% if stats and stats|length > 0 %}
        <div class="row mt-3">
            <!-- Overall Statistics -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar me-2"></i>Overall Statistics</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><i class="fas fa-futbol me-2"></i>Matches Played:</td>
                                <td><strong>{{ stats.total_played }}</strong></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-trophy me-2 text-success"></i>Wins:</td>
                                <td><strong class="text-success">{{ stats.total_wins }}</strong></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-handshake me-2 text-warning"></i>Draws:</td>
                                <td><strong class="text-warning">{{ stats.total_draws }}</strong></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-times me-2 text-danger"></i>Losses:</td>
                                <td><strong class="text-danger">{{ stats.total_losses }}</strong></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-star me-2"></i>Points/Game:</td>
                                <td><strong>{{ "%.2f"|format(stats.points_per_game) }}</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Goals Statistics -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bullseye me-2"></i>Goals Statistics</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><i class="fas fa-futbol me-2 text-success"></i>Goals Scored:</td>
                                <td><strong class="text-success">{{ stats.goals_scored_all }}</strong></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-shield-alt me-2 text-danger"></i>Goals Conceded:</td>
                                <td><strong class="text-danger">{{ stats.goals_conceded_all }}</strong></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-chart-line me-2"></i>Goals/Match:</td>
                                <td><strong>{{ "%.2f"|format(stats.goals_scored_per_match_all) }}</strong></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-chart-line me-2"></i>Conceded/Match:</td>
                                <td><strong>{{ "%.2f"|format(stats.goals_conceded_per_match_all) }}</strong></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-plus-minus me-2"></i>Goal Difference:</td>
                                <td><strong class="{% if (stats.goals_scored_all - stats.goals_conceded_all) > 0 %}text-success{% elif (stats.goals_scored_all - stats.goals_conceded_all) < 0 %}text-danger{% endif %}">
                                    {{ stats.goals_scored_all - stats.goals_conceded_all }}
                                </strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Home vs Away -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-home me-2"></i>Home Performance</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><i class="fas fa-star me-2"></i>Points/Game:</td>
                                <td><strong>{{ "%.2f"|format(stats.home_points_per_game) }}</strong></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-futbol me-2"></i>Goals Scored:</td>
                                <td><strong>{{ stats.goals_scored_home }}</strong></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-shield-alt me-2"></i>Goals Conceded:</td>
                                <td><strong>{{ stats.goals_conceded_home }}</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-plane me-2"></i>Away Performance</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><i class="fas fa-star me-2"></i>Points/Game:</td>
                                <td><strong>{{ "%.2f"|format(stats.away_points_per_game) }}</strong></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-futbol me-2"></i>Goals Scored:</td>
                                <td><strong>{{ stats.goals_scored_away }}</strong></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-shield-alt me-2"></i>Goals Conceded:</td>
                                <td><strong>{{ stats.goals_conceded_away }}</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Recent Form Stats -->
            {% if stats.ppg_last_8 %}
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-fire me-2"></i>Recent Form (Last 8 Matches)</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-primary">{{ "%.2f"|format(stats.ppg_last_8) }}</h4>
                                    <p class="text-muted">Points/Game</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-success">{{ "%.2f"|format(stats.avg_goals_scored_last_8) }}</h4>
                                    <p class="text-muted">Goals Scored/Game</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-danger">{{ "%.2f"|format(stats.avg_goals_conceded_last_8) }}</h4>
                                    <p class="text-muted">Goals Conceded/Game</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
        {% else %}
        <div class="alert alert-info mt-3">
            <i class="fas fa-info-circle me-2"></i>No statistics available for this team.
        </div>
        {% endif %}
    </div>

    <!-- Recent Form Tab -->
    <div class="tab-pane fade" id="form-content" role="tabpanel">
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-chart-line me-2"></i>Recent Matches</h5>
            </div>
            <div class="card-body">
                {% if recent_form %}
                <div class="row">
                    {% for match in recent_form %}
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-3 text-end">
                                        <strong {% if match.venue == 'Home' %}class="text-primary"{% endif %}>
                                            {{ match.home_team }}
                                        </strong>
                                    </div>
                                    <div class="col-3 text-center">
                                        {% if match.home_score is not none and match.away_score is not none %}
                                        <span class="badge bg-primary fs-6">
                                            {{ match.home_score }} - {{ match.away_score }}
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary">TBD</span>
                                        {% endif %}
                                    </div>
                                    <div class="col-3">
                                        <strong {% if match.venue == 'Away' %}class="text-primary"{% endif %}>
                                            {{ match.away_team }}
                                        </strong>
                                    </div>
                                    <div class="col-3 text-center">
                                        {% if match.result %}
                                        <span class="badge 
                                            {% if match.result == 'W' %}bg-success
                                            {% elif match.result == 'D' %}bg-warning
                                            {% else %}bg-danger{% endif %}">
                                            {{ match.result }}
                                        </span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="text-center mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>{{ match.match_date }}
                                        <i class="fas fa-map-marker-alt ms-2 me-1"></i>{{ match.venue }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted">No recent form data available</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Predictions Tab -->
    <div class="tab-pane fade" id="predictions-content" role="tabpanel">
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-crystal-ball me-2"></i>Match Predictions</h5>
            </div>
            <div class="card-body">
                <form id="predictionForm">
                    <div class="row">
                        <div class="col-md-8">
                            <label for="opponent" class="form-label">Select Opponent</label>
                            <select class="form-select" id="predictionOpponent" name="opponent">
                                <option value="">Choose opponent...</option>
                                {% for team in other_teams %}
                                <option value="{{ team }}">{{ team }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <div class="btn-group w-100">
                                <button type="button" class="btn btn-primary" onclick="predictAsHome()">
                                    <i class="fas fa-home me-2"></i>{{ team_name }} Home
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="predictAsAway()">
                                    <i class="fas fa-plane me-2"></i>{{ team_name }} Away
                                </button>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="mt-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-home me-2"></i>Home Advantage</h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">Predict {{ team_name }} playing at home against any opponent.</p>
                                    <small class="text-muted">Home teams typically have better performance due to familiar conditions and crowd support.</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-plane me-2"></i>Away Challenge</h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">Predict {{ team_name }} playing away against any opponent.</p>
                                    <small class="text-muted">Away games present additional challenges but can reveal team resilience.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-3 text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>
                    Select an opponent and choose home/away to get AI-powered match predictions
                </div>
            </div>
        </div>

        <!-- Quick Prediction Links -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-bolt me-2"></i>Quick Predictions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for team in other_teams[:6] %}
                    <div class="col-md-4 mb-2">
                        <div class="d-grid gap-2">
                            <div class="btn-group">
                                <a href="/predict/{{ league_name }}/{{ team_name }}/{{ team }}"
                                   class="btn btn-sm btn-outline-success" title="{{ team_name }} (Home) vs {{ team }}">
                                    <i class="fas fa-home"></i>
                                </a>
                                <span class="btn btn-sm btn-outline-secondary disabled">vs {{ team }}</span>
                                <a href="/predict/{{ league_name }}/{{ team }}/{{ team_name }}"
                                   class="btn btn-sm btn-outline-info" title="{{ team }} (Home) vs {{ team_name }}">
                                    <i class="fas fa-plane"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if other_teams|length > 6 %}
                <div class="text-center mt-3">
                    <a href="/predictions/{{ league_name }}" class="btn btn-primary">
                        <i class="fas fa-chart-line me-2"></i>View All Predictions
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Head-to-Head Tab -->
    <div class="tab-pane fade" id="h2h-content" role="tabpanel">
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-balance-scale me-2"></i>Head-to-Head Comparison</h5>
            </div>
            <div class="card-body">
                <form id="h2hForm">
                    <div class="row">
                        <div class="col-md-8">
                            <label for="opponent" class="form-label">Select Opponent</label>
                            <select class="form-select" id="opponent" name="opponent">
                                <option value="">Choose opponent...</option>
                                {% for team in other_teams %}
                                <option value="{{ team }}">{{ team }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-balance-scale me-2"></i>Compare
                            </button>
                        </div>
                    </div>
                </form>
                
                <div class="mt-3 text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>
                    Select an opponent to view head-to-head statistics
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_scripts %}
<script>
// Head-to-Head form submission
document.getElementById('h2hForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const opponent = document.getElementById('opponent').value;

    if (opponent) {
        window.location.href = `/h2h/{{ league_name }}/${encodeURIComponent('{{ team_name }}')}/${encodeURIComponent(opponent)}`;
    } else {
        alert('Please select an opponent');
    }
});

// Prediction functions
function predictAsHome() {
    const opponent = document.getElementById('predictionOpponent').value;

    if (opponent) {
        window.location.href = `/predict/{{ league_name }}/${encodeURIComponent('{{ team_name }}')}/${encodeURIComponent(opponent)}`;
    } else {
        alert('Please select an opponent first');
    }
}

function predictAsAway() {
    const opponent = document.getElementById('predictionOpponent').value;

    if (opponent) {
        window.location.href = `/predict/{{ league_name }}/${encodeURIComponent(opponent)}/${encodeURIComponent('{{ team_name }}')}`;
    } else {
        alert('Please select an opponent first');
    }
}

// Sync opponent selection between H2H and Predictions
document.getElementById('opponent').addEventListener('change', function() {
    document.getElementById('predictionOpponent').value = this.value;
});

document.getElementById('predictionOpponent').addEventListener('change', function() {
    document.getElementById('opponent').value = this.value;
});
</script>
{% endblock %}