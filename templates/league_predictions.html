{% extends "base.html" %}

{% block title %}{{ league_name.replace('_', ' ').title() }} - Predictions{% endblock %}

{% block extra_css %}
<style>
    .prediction-interface {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    
    .team-selector {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    
    .team-select {
        background: rgba(255,255,255,0.9);
        border: none;
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 1.1rem;
        width: 100%;
        color: #333;
    }
    
    .predict-btn {
        background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
        border: none;
        border-radius: 25px;
        padding: 1rem 2rem;
        font-size: 1.2rem;
        font-weight: bold;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .predict-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        color: white;
    }
    
    .predict-btn:disabled {
        background: #ccc;
        transform: none;
        box-shadow: none;
    }
    
    .recent-matches {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .match-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        border-bottom: 1px solid #eee;
        transition: background-color 0.3s ease;
    }
    
    .match-item:hover {
        background-color: #f8f9fa;
        cursor: pointer;
    }
    
    .match-item:last-child {
        border-bottom: none;
    }
    
    .match-teams {
        font-weight: bold;
        color: #333;
    }
    
    .match-score {
        font-size: 1.2rem;
        color: #666;
    }
    
    .match-date {
        color: #888;
        font-size: 0.9rem;
    }
    
    .quick-predictions {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .quick-match {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        border: 1px solid #ddd;
        border-radius: 8px;
        margin: 0.5rem 0;
        transition: all 0.3s ease;
    }
    
    .quick-match:hover {
        border-color: #667eea;
        box-shadow: 0 3px 10px rgba(102, 126, 234, 0.2);
    }
    
    .vs-icon {
        color: #667eea;
        font-size: 1.2rem;
        margin: 0 1rem;
    }
    
    .loading-spinner {
        display: none;
        margin-left: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item"><a href="/league/{{ league_name }}">{{ league_name.replace('_', ' ').title() }}</a></li>
            <li class="breadcrumb-item active">Predictions</li>
        </ol>
    </nav>

    <!-- Main Prediction Interface -->
    <div class="prediction-interface">
        <div class="row">
            <div class="col-md-12 text-center">
                <h1 class="mb-4">
                    <i class="fas fa-crystal-ball me-2"></i>
                    {{ league_name.replace('_', ' ').title() }} Predictions
                </h1>
                <p class="lead">Predict match outcomes using advanced AI models</p>
            </div>
        </div>

        <div class="team-selector">
            <form id="predictionForm">
                <div class="row">
                    <div class="col-md-5">
                        <label for="homeTeam" class="form-label">
                            <i class="fas fa-home me-2"></i>Home Team
                        </label>
                        <select class="team-select" id="homeTeam" name="homeTeam" required>
                            <option value="">Select Home Team</option>
                            {% for team in teams %}
                            <option value="{{ team }}">{{ team }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-2 text-center d-flex align-items-end justify-content-center">
                        <div class="vs-icon">
                            <i class="fas fa-vs"></i>
                        </div>
                    </div>
                    
                    <div class="col-md-5">
                        <label for="awayTeam" class="form-label">
                            <i class="fas fa-plane me-2"></i>Away Team
                        </label>
                        <select class="team-select" id="awayTeam" name="awayTeam" required>
                            <option value="">Select Away Team</option>
                            {% for team in teams %}
                            <option value="{{ team }}">{{ team }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12 text-center">
                        <button type="submit" class="predict-btn" id="predictButton">
                            <i class="fas fa-magic me-2"></i>
                            Predict Match
                            <div class="loading-spinner">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Quick Predictions for Popular Matchups -->
    {% if teams|length >= 4 %}
    <div class="quick-predictions">
        <h3><i class="fas fa-bolt me-2"></i>Quick Predictions</h3>
        <p class="text-muted">Click on any matchup to get instant predictions</p>
        
        <div class="row">
            {% set popular_teams = teams[:6] %}
            {% for i in range(0, popular_teams|length - 1, 2) %}
            {% if i + 1 < popular_teams|length %}
            <div class="col-md-6">
                <div class="quick-match" onclick="quickPredict('{{ popular_teams[i] }}', '{{ popular_teams[i+1] }}')">
                    <span class="match-teams">{{ popular_teams[i] }}</span>
                    <span class="vs-icon"><i class="fas fa-vs"></i></span>
                    <span class="match-teams">{{ popular_teams[i+1] }}</span>
                    <button class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-chart-line"></i>
                    </button>
                </div>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Recent Matches -->
    {% if recent_matches %}
    <div class="recent-matches">
        <h3><i class="fas fa-history me-2"></i>Recent Matches</h3>
        <p class="text-muted">Click on any match to see what our model would have predicted</p>
        
        {% for match in recent_matches %}
        <div class="match-item" onclick="quickPredict('{{ match.home_team }}', '{{ match.away_team }}')">
            <div>
                <div class="match-teams">{{ match.home_team }} vs {{ match.away_team }}</div>
                <div class="match-date">{{ match.match_date }}</div>
            </div>
            <div>
                {% if match.home_score is not none and match.away_score is not none %}
                <div class="match-score">{{ match.home_score }} - {{ match.away_score }}</div>
                {% else %}
                <div class="match-score">-</div>
                {% endif %}
            </div>
            <div>
                <button class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-chart-line"></i> Predict
                </button>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-md-12 text-center">
            <a href="/league/{{ league_name }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to League
            </a>
            <a href="/leagues" class="btn btn-outline-primary">
                <i class="fas fa-list me-2"></i>All Leagues
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('predictionForm');
    const homeTeam = document.getElementById('homeTeam');
    const awayTeam = document.getElementById('awayTeam');
    const predictButton = document.getElementById('predictButton');
    const loadingSpinner = document.querySelector('.loading-spinner');
    
    // Prevent selecting the same team for both home and away
    function updateTeamOptions() {
        const homeValue = homeTeam.value;
        const awayValue = awayTeam.value;
        
        // Enable/disable options based on selection
        Array.from(awayTeam.options).forEach(option => {
            option.disabled = option.value === homeValue && option.value !== '';
        });
        
        Array.from(homeTeam.options).forEach(option => {
            option.disabled = option.value === awayValue && option.value !== '';
        });
        
        // Update button state
        predictButton.disabled = !homeValue || !awayValue || homeValue === awayValue;
    }
    
    homeTeam.addEventListener('change', updateTeamOptions);
    awayTeam.addEventListener('change', updateTeamOptions);
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const homeTeamValue = homeTeam.value;
        const awayTeamValue = awayTeam.value;
        
        if (!homeTeamValue || !awayTeamValue || homeTeamValue === awayTeamValue) {
            alert('Please select two different teams');
            return;
        }
        
        // Show loading state
        predictButton.disabled = true;
        loadingSpinner.style.display = 'inline-block';
        
        // Navigate to prediction page
        window.location.href = `/predict/{{ league_name }}/${encodeURIComponent(homeTeamValue)}/${encodeURIComponent(awayTeamValue)}`;
    });
    
    // Initialize
    updateTeamOptions();
});

function quickPredict(homeTeam, awayTeam) {
    // Show loading state
    const button = event.target.closest('.match-item, .quick-match').querySelector('button');
    if (button) {
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;
    }
    
    // Navigate to prediction
    window.location.href = `/predict/{{ league_name }}/${encodeURIComponent(homeTeam)}/${encodeURIComponent(awayTeam)}`;
}
</script>
{% endblock %}
