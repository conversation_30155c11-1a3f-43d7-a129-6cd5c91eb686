{% extends "base.html" %}

{% block title %}{{ league_name.replace('_', ' ').title() }} - Predictions{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        --shadow-light: 0 5px 15px rgba(0,0,0,0.1);
        --shadow-medium: 0 10px 30px rgba(0,0,0,0.2);
        --shadow-heavy: 0 15px 35px rgba(0,0,0,0.3);
        --border-radius: 15px;
        --border-radius-small: 8px;
    }

    .prediction-interface {
        background: var(--primary-gradient);
        color: white;
        border-radius: var(--border-radius);
        padding: 3rem 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-heavy);
        position: relative;
        overflow: hidden;
    }

    .prediction-interface::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
        pointer-events: none;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .team-selector {
        background: rgba(255,255,255,0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        border-radius: var(--border-radius-small);
        padding: 2rem;
        margin: 1.5rem 0;
        position: relative;
        z-index: 1;
    }

    .team-select {
        background: rgba(255,255,255,0.95);
        border: 2px solid transparent;
        border-radius: var(--border-radius-small);
        padding: 1rem;
        font-size: 1.1rem;
        width: 100%;
        color: #333;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-light);
    }

    .team-select:focus {
        outline: none;
        border-color: #4ECDC4;
        box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.3);
        transform: translateY(-2px);
    }

    .predict-btn {
        background: var(--secondary-gradient);
        border: none;
        border-radius: 30px;
        padding: 1.2rem 2.5rem;
        font-size: 1.2rem;
        font-weight: bold;
        color: white;
        transition: all 0.4s ease;
        box-shadow: var(--shadow-medium);
        position: relative;
        overflow: hidden;
    }

    .predict-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }

    .predict-btn:hover::before {
        left: 100%;
    }

    .predict-btn:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-heavy);
        color: white;
    }

    .predict-btn:disabled {
        background: linear-gradient(135deg, #ccc, #999);
        transform: none;
        box-shadow: var(--shadow-light);
        cursor: not-allowed;
    }

    .recent-matches {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin: 2rem 0;
        box-shadow: var(--shadow-medium);
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .recent-matches h3 {
        color: #667eea;
        margin-bottom: 1.5rem;
        font-weight: 600;
    }

    .match-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        border-radius: var(--border-radius-small);
        margin-bottom: 0.5rem;
    }

    .match-item:hover {
        background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
        cursor: pointer;
        transform: translateX(5px);
        box-shadow: var(--shadow-light);
    }

    .match-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .match-teams {
        font-weight: 600;
        color: #333;
        font-size: 1.1rem;
    }

    .match-score {
        font-size: 1.3rem;
        color: #667eea;
        font-weight: bold;
        background: rgba(102, 126, 234, 0.1);
        padding: 0.5rem 1rem;
        border-radius: 20px;
    }

    .match-date {
        color: #888;
        font-size: 0.9rem;
        font-style: italic;
    }

    .quick-predictions {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin: 2rem 0;
        box-shadow: var(--shadow-medium);
        border: 1px solid rgba(245, 87, 108, 0.1);
    }

    .quick-predictions h3 {
        color: #f5576c;
        margin-bottom: 1.5rem;
        font-weight: 600;
    }

    .quick-match {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border: 2px solid transparent;
        border-radius: var(--border-radius-small);
        margin: 1rem 0;
        transition: all 0.4s ease;
        background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
        position: relative;
        overflow: hidden;
    }

    .quick-match::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--success-gradient);
        opacity: 0.1;
        transition: left 0.5s ease;
    }

    .quick-match:hover::before {
        left: 0;
    }

    .quick-match:hover {
        border-color: #4facfe;
        box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
        transform: translateY(-3px);
    }

    .vs-icon {
        color: #667eea;
        font-size: 1.5rem;
        margin: 0 1.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    .loading-spinner {
        display: none;
        margin-left: 10px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Enhanced form styling */
    .form-label {
        font-weight: 600;
        color: rgba(255,255,255,0.9);
        margin-bottom: 0.8rem;
        font-size: 1.1rem;
    }

    /* Button enhancements */
    .btn-outline-primary {
        border: 2px solid #4facfe;
        color: #4facfe;
        background: transparent;
        transition: all 0.3s ease;
    }

    .btn-outline-primary:hover {
        background: var(--success-gradient);
        border-color: transparent;
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }

    /* Card enhancements */
    .card {
        border: none;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-light);
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }

    /* Breadcrumb styling */
    .breadcrumb {
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        border-radius: var(--border-radius-small);
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
    }

    .breadcrumb-item a {
        color: #667eea;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .breadcrumb-item a:hover {
        color: #f5576c;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .prediction-interface {
            padding: 2rem 1rem;
        }

        .team-selector {
            padding: 1.5rem;
        }

        .predict-btn {
            padding: 1rem 2rem;
            font-size: 1rem;
        }

        .vs-icon {
            margin: 0 0.5rem;
            font-size: 1.2rem;
        }

        .quick-match {
            padding: 1rem;
        }

        .match-item {
            padding: 1rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item"><a href="/league/{{ league_name }}">{{ league_name.replace('_', ' ').title() }}</a></li>
            <li class="breadcrumb-item active">Predictions</li>
        </ol>
    </nav>

    <!-- Main Prediction Interface -->
    <div class="prediction-interface">
        <div class="row">
            <div class="col-md-12 text-center">
                <h1 class="mb-4">
                    <i class="fas fa-crystal-ball me-2"></i>
                    {{ league_name.replace('_', ' ').title() }} Predictions
                </h1>
                <p class="lead">Predict match outcomes using advanced AI models</p>
            </div>
        </div>

        <div class="team-selector">
            <form id="predictionForm">
                <div class="row">
                    <div class="col-md-5">
                        <label for="homeTeam" class="form-label">
                            <i class="fas fa-home me-2"></i>Home Team
                        </label>
                        <select class="team-select" id="homeTeam" name="homeTeam" required>
                            <option value="">Select Home Team</option>
                            {% for team in teams %}
                            <option value="{{ team }}">{{ team }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-2 text-center d-flex align-items-end justify-content-center">
                        <div class="vs-icon">
                            <i class="fas fa-vs"></i>
                        </div>
                    </div>
                    
                    <div class="col-md-5">
                        <label for="awayTeam" class="form-label">
                            <i class="fas fa-plane me-2"></i>Away Team
                        </label>
                        <select class="team-select" id="awayTeam" name="awayTeam" required>
                            <option value="">Select Away Team</option>
                            {% for team in teams %}
                            <option value="{{ team }}">{{ team }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12 text-center">
                        <button type="submit" class="predict-btn" id="predictButton">
                            <i class="fas fa-magic me-2"></i>
                            Predict Match
                            <div class="loading-spinner">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Quick Predictions for Popular Matchups -->
    {% if teams|length >= 4 %}
    <div class="quick-predictions">
        <h3><i class="fas fa-bolt me-2"></i>Quick Predictions</h3>
        <p class="text-muted">Click on any matchup to get instant predictions</p>
        
        <div class="row">
            {% set popular_teams = teams[:6] %}
            {% for i in range(0, popular_teams|length - 1, 2) %}
            {% if i + 1 < popular_teams|length %}
            <div class="col-md-6">
                <div class="quick-match" onclick="quickPredict('{{ popular_teams[i] }}', '{{ popular_teams[i+1] }}')">
                    <span class="match-teams">{{ popular_teams[i] }}</span>
                    <span class="vs-icon"><i class="fas fa-vs"></i></span>
                    <span class="match-teams">{{ popular_teams[i+1] }}</span>
                    <button class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-chart-line"></i>
                    </button>
                </div>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Recent Matches -->
    {% if recent_matches %}
    <div class="recent-matches">
        <h3><i class="fas fa-history me-2"></i>Recent Matches</h3>
        <p class="text-muted">Click on any match to see what our model would have predicted</p>
        
        {% for match in recent_matches %}
        <div class="match-item" onclick="quickPredict('{{ match.home_team }}', '{{ match.away_team }}')">
            <div>
                <div class="match-teams">{{ match.home_team }} vs {{ match.away_team }}</div>
                <div class="match-date">{{ match.match_date }}</div>
            </div>
            <div>
                {% if match.home_score is not none and match.away_score is not none %}
                <div class="match-score">{{ match.home_score }} - {{ match.away_score }}</div>
                {% else %}
                <div class="match-score">-</div>
                {% endif %}
            </div>
            <div>
                <button class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-chart-line"></i> Predict
                </button>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-md-12 text-center">
            <a href="/league/{{ league_name }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to League
            </a>
            <a href="/leagues" class="btn btn-outline-primary">
                <i class="fas fa-list me-2"></i>All Leagues
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('predictionForm');
    const homeTeam = document.getElementById('homeTeam');
    const awayTeam = document.getElementById('awayTeam');
    const predictButton = document.getElementById('predictButton');
    const loadingSpinner = document.querySelector('.loading-spinner');
    
    // Prevent selecting the same team for both home and away
    function updateTeamOptions() {
        const homeValue = homeTeam.value;
        const awayValue = awayTeam.value;
        
        // Enable/disable options based on selection
        Array.from(awayTeam.options).forEach(option => {
            option.disabled = option.value === homeValue && option.value !== '';
        });
        
        Array.from(homeTeam.options).forEach(option => {
            option.disabled = option.value === awayValue && option.value !== '';
        });
        
        // Update button state
        predictButton.disabled = !homeValue || !awayValue || homeValue === awayValue;
    }
    
    homeTeam.addEventListener('change', updateTeamOptions);
    awayTeam.addEventListener('change', updateTeamOptions);
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const homeTeamValue = homeTeam.value;
        const awayTeamValue = awayTeam.value;
        
        if (!homeTeamValue || !awayTeamValue || homeTeamValue === awayTeamValue) {
            alert('Please select two different teams');
            return;
        }
        
        // Show loading state
        predictButton.disabled = true;
        loadingSpinner.style.display = 'inline-block';
        
        // Navigate to prediction page
        window.location.href = `/predict/{{ league_name }}/${encodeURIComponent(homeTeamValue)}/${encodeURIComponent(awayTeamValue)}`;
    });
    
    // Initialize
    updateTeamOptions();
});

function quickPredict(homeTeam, awayTeam) {
    // Show loading state
    const button = event.target.closest('.match-item, .quick-match').querySelector('button');
    if (button) {
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;
    }
    
    // Navigate to prediction
    window.location.href = `/predict/{{ league_name }}/${encodeURIComponent(homeTeam)}/${encodeURIComponent(awayTeam)}`;
}
</script>
{% endblock %}
