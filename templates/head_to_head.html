{% extends "base_with_team_sidebar.html" %}

{% block title %}{{ team1 }} vs {{ team2 }} - Head-to-Head{% endblock %}

{% block content %}
<!-- H2H Header -->
<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-3">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('leagues') }}">Leagues</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('league_detail', league_name=league_name) }}">{{ league_name.replace('_', ' ').title() }}</a></li>
                <li class="breadcrumb-item active">{{ team1 }} vs {{ team2 }}</li>
            </ol>
        </nav>
        
        <div class="text-center">
            <h2 class="mb-2">
                <span class="text-primary">{{ team1 }}</span>
                <i class="fas fa-balance-scale mx-3 text-muted"></i>
                <span class="text-danger">{{ team2 }}</span>
            </h2>
            <p class="lead text-muted">Head-to-Head Analysis</p>
        </div>
    </div>
</div>

{% if h2h and h2h|length > 0 %}
<!-- H2H Summary Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
        <div class="card bg-info text-white h2h-summary-card">
            <div class="card-body text-center py-3">
                <i class="fas fa-futbol fa-lg mb-2"></i>
                <h4 class="mb-1">{{ h2h.total_matches }}</h4>
                <p class="card-text mb-0">Total Matches</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
        <div class="card bg-primary text-white h2h-summary-card">
            <div class="card-body text-center py-3">
                <i class="fas fa-trophy fa-lg mb-2"></i>
                <h4 class="mb-1">{{ "%.1f"|format(h2h.home_win_percentage) }}%</h4>
                <p class="card-text mb-0">{{ h2h.home_team }} Wins</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
        <div class="card bg-danger text-white h2h-summary-card">
            <div class="card-body text-center py-3">
                <i class="fas fa-trophy fa-lg mb-2"></i>
                <h4 class="mb-1">{{ "%.1f"|format(h2h.away_win_percentage) }}%</h4>
                <p class="card-text mb-0">{{ h2h.away_team }} Wins</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
        <div class="card bg-warning text-white h2h-summary-card">
            <div class="card-body text-center py-3">
                <i class="fas fa-handshake fa-lg mb-2"></i>
                <h4 class="mb-1">{{ "%.1f"|format(h2h.draw_percentage) }}%</h4>
                <p class="card-text mb-0">Draws</p>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Statistics -->
<div class="row">
    <!-- Win/Loss Record with Pie Chart -->
    <div class="col-lg-6 mb-4">
        <div class="card h2h-balanced-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Match Results</h5>
            </div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-4">
                        <div class="p-3 bg-primary text-white rounded">
                            <h5 class="mb-1">{{ h2h.home_wins }}</h5>
                            <small>{{ h2h.home_team }} Wins</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="p-3 bg-warning text-white rounded">
                            <h5 class="mb-1">{{ h2h.draws }}</h5>
                            <small>Draws</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="p-3 bg-danger text-white rounded">
                            <h5 class="mb-1">{{ h2h.away_wins }}</h5>
                            <small>{{ h2h.away_team }} Wins</small>
                        </div>
                    </div>
                </div>
                
                <!-- Win Percentage Chart -->
                <div class="chart-container">
                    <canvas id="winChart" width="400" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Goals & Betting Statistics -->
    <div class="col-lg-6 mb-4">
        <div class="card h2h-balanced-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Goals & Betting Markets</h5>
            </div>
            <div class="card-body">
                <h6 class="text-primary mb-3"><i class="fas fa-futbol me-2"></i>Goal Statistics</h6>
                <table class="table table-sm mb-3">
                    <tr>
                        <td><i class="fas fa-futbol me-2 text-primary"></i>{{ h2h.home_team }} Goals:</td>
                        <td class="text-end"><strong class="text-primary">{{ h2h.home_goals }}</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-futbol me-2 text-danger"></i>{{ h2h.away_team }} Goals:</td>
                        <td class="text-end"><strong class="text-danger">{{ h2h.away_goals }}</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-chart-line me-2"></i>Avg Goals/Match:</td>
                        <td class="text-end"><strong>{{ "%.2f"|format((h2h.home_goals + h2h.away_goals) / h2h.total_matches if h2h.total_matches > 0 else 0) }}</strong></td>
                    </tr>
                </table>
                
                <h6 class="text-success mb-3"><i class="fas fa-chart-bar me-2"></i>Betting Markets</h6>
                <table class="table table-sm mb-0">
                    <tr>
                        <td><i class="fas fa-exchange-alt me-2 text-success"></i>Both Teams Score:</td>
                        <td class="text-end"><strong class="text-success">{{ "%.1f"|format(h2h.btts_percentage) }}%</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-arrow-up me-2"></i>Over 1.5 Goals:</td>
                        <td class="text-end"><strong>{{ "%.1f"|format(h2h.over_1_5_percentage) }}%</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-arrow-up me-2"></i>Over 2.5 Goals:</td>
                        <td class="text-end"><strong>{{ "%.1f"|format(h2h.over_2_5_percentage) }}%</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-arrow-up me-2"></i>Over 3.5 Goals:</td>
                        <td class="text-end"><strong>{{ "%.1f"|format(h2h.over_3_5_percentage) }}%</strong></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Clean Sheets & Team Comparison Row -->
<div class="row">
    <!-- Clean Sheets -->
    <div class="col-lg-6 mb-4">
        <div class="card h2h-balanced-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Defensive Records</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm mb-0">
                    <tr>
                        <td><i class="fas fa-shield-alt me-2 text-primary"></i>{{ h2h.home_team }} Clean Sheets:</td>
                        <td class="text-end"><strong class="text-primary">{{ "%.1f"|format(h2h.home_clean_sheet_percentage) }}%</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-shield-alt me-2 text-danger"></i>{{ h2h.away_team }} Clean Sheets:</td>
                        <td class="text-end"><strong class="text-danger">{{ "%.1f"|format(h2h.away_clean_sheet_percentage) }}%</strong></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Current Season Comparison -->
    <div class="col-lg-6 mb-4">
        <div class="card h2h-balanced-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-balance-scale me-2"></i>Current Season Comparison</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <h6 class="text-primary mb-2">{{ team1 }}</h6>
                        {% if team1_stats %}
                        <table class="table table-sm table-borderless mb-0">
                            <tr>
                                <td>Goals/Match:</td>
                                <td class="text-end"><strong>{{ "%.2f"|format(team1_stats.goals_scored_per_match_all) }}</strong></td>
                            </tr>
                            <tr>
                                <td>Conceded/Match:</td>
                                <td class="text-end"><strong>{{ "%.2f"|format(team1_stats.goals_conceded_per_match_all) }}</strong></td>
                            </tr>
                            <tr>
                                <td>Points/Game:</td>
                                <td class="text-end"><strong>{{ "%.2f"|format(team1_stats.points_per_game) }}</strong></td>
                            </tr>
                        </table>
                        {% else %}
                        <p class="text-muted small">No current season data</p>
                        {% endif %}
                    </div>
                    <div class="col-6">
                        <h6 class="text-danger mb-2">{{ team2 }}</h6>
                        {% if team2_stats %}
                        <table class="table table-sm table-borderless mb-0">
                            <tr>
                                <td>Goals/Match:</td>
                                <td class="text-end"><strong>{{ "%.2f"|format(team2_stats.goals_scored_per_match_all) }}</strong></td>
                            </tr>
                            <tr>
                                <td>Conceded/Match:</td>
                                <td class="text-end"><strong>{{ "%.2f"|format(team2_stats.goals_conceded_per_match_all) }}</strong></td>
                            </tr>
                            <tr>
                                <td>Points/Game:</td>
                                <td class="text-end"><strong>{{ "%.2f"|format(team2_stats.points_per_game) }}</strong></td>
                            </tr>
                        </table>
                        {% else %}
                        <p class="text-muted small">No current season data</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Recent Results -->
{% if h2h.recent_results %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card h2h-balanced-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Head-to-Head Results</h5>
            </div>
            <div class="card-body">
                <div class="recent-results-container">
                    {% set results_list = h2h.recent_results.split(',') %}
                    {% if results_list|length > 1 %}
                        <div class="row">
                            {% for result in results_list %}
                                {% if result.strip() %}
                                    {% set parts = result.strip().split(' ') %}
                                    {% if parts|length >= 3 %}
                                        <div class="col-lg-6 col-md-12 mb-3">
                                            <div class="recent-match-card">
                                                <div class="match-teams">
                                                    <div class="team-score">
                                                        <span class="team-name text-primary">{{ team1 }}</span>
                                                        <span class="score">
                                                            {% if parts[1] and parts[1]|int >= 0 %}
                                                                {{ parts[1] }}
                                                            {% else %}
                                                                -
                                                            {% endif %}
                                                        </span>
                                                    </div>
                                                    <div class="vs-divider">
                                                        <i class="fas fa-circle"></i>
                                                    </div>
                                                    <div class="team-score">
                                                        <span class="score">
                                                            {% if parts[2] and parts[2]|int >= 0 %}
                                                                {{ parts[2] }}
                                                            {% else %}
                                                                -
                                                            {% endif %}
                                                        </span>
                                                        <span class="team-name text-danger">{{ team2 }}</span>
                                                    </div>
                                                </div>
                                                <div class="match-result">
                                                    {% set score1 = parts[1]|int if parts[1] and parts[1]|int >= 0 else 0 %}
                                                    {% set score2 = parts[2]|int if parts[2] and parts[2]|int >= 0 else 0 %}
                                                    {% if score1 > score2 %}
                                                        <span class="result-badge win">{{ team1 }} Win</span>
                                                    {% elif score2 > score1 %}
                                                        <span class="result-badge loss">{{ team2 }} Win</span>
                                                    {% else %}
                                                        <span class="result-badge draw">Draw</span>
                                                    {% endif %}
                                                </div>
                                                {% if parts|length > 3 %}
                                                <div class="match-date">
                                                    <i class="fas fa-calendar-alt me-1"></i>
                                                    <small class="text-muted">{{ parts[3:] | join(' ') }}</small>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        </div>
                    {% else %}
                        <!-- Fallback for unstructured data -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Recent Results:</strong> {{ h2h.recent_results }}
                        </div>
                    {% endif %}
                </div>
                
                <div class="text-center mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Showing most recent head-to-head encounters between these teams
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% else %}
<!-- No H2H Data -->
<div class="row">
    <div class="col-12">
        <div class="alert alert-warning text-center">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h4>No Head-to-Head Data Available</h4>
            <p>We don't have historical matchup data for {{ team1 }} vs {{ team2 }} in {{ league_name.replace('_', ' ').title() }}.</p>
            <a href="{{ url_for('league_detail', league_name=league_name) }}" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to League
            </a>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block extra_scripts %}
{% if h2h %}
<script>
// Win Percentage Pie Chart
const winCtx = document.getElementById('winChart').getContext('2d');
new Chart(winCtx, {
    type: 'doughnut',
    data: {
        labels: ['{{ h2h.home_team }}', 'Draw', '{{ h2h.away_team }}'],
        datasets: [{
            data: [{{ h2h.home_win_percentage }}, {{ h2h.draw_percentage }}, {{ h2h.away_win_percentage }}],
            backgroundColor: ['#0d6efd', '#ffc107', '#dc3545'],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 15,
                    usePointStyle: true
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': ' + context.parsed + '%';
                    }
                }
            }
        }
    }
});
</script>
{% endif %}
{% endblock %}