{% extends "base_with_team_sidebar.html" %}

{% block title %}{{ team1 }} vs {{ team2 }} - Head-to-Head{% endblock %}

{% block content %}
<!-- H2H Header -->
<div class="row mb-3">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-2">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('leagues') }}">Leagues</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('league_detail', league_name=league_name) }}">{{ league_name.replace('_', ' ').title() }}</a></li>
                <li class="breadcrumb-item active">{{ team1 }} vs {{ team2 }}</li>
            </ol>
        </nav>
        
        <div class="text-center">
            <h3 class="mb-1">
                <span class="text-primary">{{ team1 }}</span>
                <i class="fas fa-balance-scale mx-2 text-muted"></i>
                <span class="text-danger">{{ team2 }}</span>
            </h3>
            <p class="text-muted mb-0">Head-to-Head Analysis</p>
        </div>
    </div>
</div>

{% if h2h and h2h|length > 0 %}
<!-- H2H Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-futbol fa-2x mb-2"></i>
                <h4>{{ h2h.total_matches }}</h4>
                <p class="card-text">Total Matches</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-trophy fa-2x mb-2"></i>
                <h4>{{ "%.1f"|format(h2h.home_win_percentage) }}%</h4>
                <p class="card-text">{{ h2h.home_team }} Wins</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <i class="fas fa-trophy fa-2x mb-2"></i>
                <h4>{{ "%.1f"|format(h2h.away_win_percentage) }}%</h4>
                <p class="card-text">{{ h2h.away_team }} Wins</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-handshake fa-2x mb-2"></i>
                <h4>{{ "%.1f"|format(h2h.draw_percentage) }}%</h4>
                <p class="card-text">Draws</p>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Statistics -->
<div class="row">
    <!-- Win/Loss Record -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie me-2"></i>Match Results</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="p-3 bg-primary text-white rounded">
                            <h3>{{ h2h.home_wins }}</h3>
                            <p class="mb-0">{{ h2h.home_team }} Wins</p>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="p-3 bg-warning text-white rounded">
                            <h3>{{ h2h.draws }}</h3>
                            <p class="mb-0">Draws</p>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="p-3 bg-danger text-white rounded">
                            <h3>{{ h2h.away_wins }}</h3>
                            <p class="mb-0">{{ h2h.away_team }} Wins</p>
                        </div>
                    </div>
                </div>
                
                <!-- Win Percentage Chart -->
                <div class="mt-4">
                    <canvas id="winChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Goals Statistics -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bullseye me-2"></i>Goals Statistics</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><i class="fas fa-futbol me-2 text-primary"></i>{{ h2h.home_team }} Goals:</td>
                        <td><strong class="text-primary">{{ h2h.home_goals }}</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-futbol me-2 text-danger"></i>{{ h2h.away_team }} Goals:</td>
                        <td><strong class="text-danger">{{ h2h.away_goals }}</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-chart-line me-2"></i>Avg Goals/Match:</td>
                        <td><strong>{{ "%.2f"|format((h2h.home_goals + h2h.away_goals) / h2h.total_matches if h2h.total_matches > 0 else 0) }}</strong></td>
                    </tr>
                </table>
                
                <!-- Goals Chart -->
                <div class="mt-3">
                    <canvas id="goalsChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Betting Statistics -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar me-2"></i>Betting Markets</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><i class="fas fa-exchange-alt me-2"></i>Both Teams Score:</td>
                        <td><strong class="text-success">{{ "%.1f"|format(h2h.btts_percentage) }}%</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-arrow-up me-2"></i>Over 1.5 Goals:</td>
                        <td><strong>{{ "%.1f"|format(h2h.over_1_5_percentage) }}%</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-arrow-up me-2"></i>Over 2.5 Goals:</td>
                        <td><strong>{{ "%.1f"|format(h2h.over_2_5_percentage) }}%</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-arrow-up me-2"></i>Over 3.5 Goals:</td>
                        <td><strong>{{ "%.1f"|format(h2h.over_3_5_percentage) }}%</strong></td>
                    </tr>
                </table>
                
                <!-- Over/Under Chart -->
                <div class="mt-3">
                    <canvas id="overUnderChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Clean Sheets -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-shield-alt me-2"></i>Clean Sheets</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><i class="fas fa-shield-alt me-2 text-primary"></i>{{ h2h.home_team }} Clean Sheets:</td>
                        <td><strong class="text-primary">{{ "%.1f"|format(h2h.home_clean_sheet_percentage) }}%</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-shield-alt me-2 text-danger"></i>{{ h2h.away_team }} Clean Sheets:</td>
                        <td><strong class="text-danger">{{ "%.1f"|format(h2h.away_clean_sheet_percentage) }}%</strong></td>
                    </tr>
                </table>
                
                <!-- Clean Sheets Chart -->
                <div class="mt-3">
                    <canvas id="cleanSheetsChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Team Comparison -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-balance-scale me-2"></i>Current Season Comparison</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">{{ team1 }}</h6>
                        {% if team1_stats %}
                        <table class="table table-sm">
                            <tr>
                                <td>Goals/Match:</td>
                                <td><strong>{{ "%.2f"|format(team1_stats.goals_scored_per_match_all) }}</strong></td>
                            </tr>
                            <tr>
                                <td>Conceded/Match:</td>
                                <td><strong>{{ "%.2f"|format(team1_stats.goals_conceded_per_match_all) }}</strong></td>
                            </tr>
                            <tr>
                                <td>Points/Game:</td>
                                <td><strong>{{ "%.2f"|format(team1_stats.points_per_game) }}</strong></td>
                            </tr>
                            <tr>
                                <td>Matches Played:</td>
                                <td><strong>{{ team1_stats.total_played }}</strong></td>
                            </tr>
                        </table>
                        {% else %}
                        <p class="text-muted">No current season data</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-danger">{{ team2 }}</h6>
                        {% if team2_stats %}
                        <table class="table table-sm">
                            <tr>
                                <td>Goals/Match:</td>
                                <td><strong>{{ "%.2f"|format(team2_stats.goals_scored_per_match_all) }}</strong></td>
                            </tr>
                            <tr>
                                <td>Conceded/Match:</td>
                                <td><strong>{{ "%.2f"|format(team2_stats.goals_conceded_per_match_all) }}</strong></td>
                            </tr>
                            <tr>
                                <td>Points/Game:</td>
                                <td><strong>{{ "%.2f"|format(team2_stats.points_per_game) }}</strong></td>
                            </tr>
                            <tr>
                                <td>Matches Played:</td>
                                <td><strong>{{ team2_stats.total_played }}</strong></td>
                            </tr>
                        </table>
                        {% else %}
                        <p class="text-muted">No current season data</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Results -->
{% if h2h.recent_results %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-history me-2"></i>Recent Results</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">{{ h2h.recent_results }}</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% else %}
<!-- No H2H Data -->
<div class="row">
    <div class="col-12">
        <div class="alert alert-warning text-center">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h4>No Head-to-Head Data Available</h4>
            <p>We don't have historical matchup data for {{ team1 }} vs {{ team2 }} in {{ league_name.replace('_', ' ').title() }}.</p>
            <a href="{{ url_for('league_detail', league_name=league_name) }}" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to League
            </a>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block extra_scripts %}
{% if h2h %}
<script>
// Win Percentage Chart
const winCtx = document.getElementById('winChart').getContext('2d');
new Chart(winCtx, {
    type: 'doughnut',
    data: {
        labels: ['{{ h2h.home_team }}', 'Draw', '{{ h2h.away_team }}'],
        datasets: [{
            data: [{{ h2h.home_win_percentage }}, {{ h2h.draw_percentage }}, {{ h2h.away_win_percentage }}],
            backgroundColor: ['#0d6efd', '#ffc107', '#dc3545']
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Goals Chart
const goalsCtx = document.getElementById('goalsChart').getContext('2d');
new Chart(goalsCtx, {
    type: 'bar',
    data: {
        labels: ['{{ h2h.home_team }}', '{{ h2h.away_team }}'],
        datasets: [{
            label: 'Total Goals',
            data: [{{ h2h.home_goals }}, {{ h2h.away_goals }}],
            backgroundColor: ['#0d6efd', '#dc3545']
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Over/Under Chart
const overUnderCtx = document.getElementById('overUnderChart').getContext('2d');
new Chart(overUnderCtx, {
    type: 'bar',
    data: {
        labels: ['Over 1.5', 'Over 2.5', 'Over 3.5', 'BTTS'],
        datasets: [{
            label: 'Percentage',
            data: [{{ h2h.over_1_5_percentage }}, {{ h2h.over_2_5_percentage }}, {{ h2h.over_3_5_percentage }}, {{ h2h.btts_percentage }}],
            backgroundColor: ['#198754', '#fd7e14', '#dc3545', '#6f42c1']
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        }
    }
});

// Clean Sheets Chart
const cleanSheetsCtx = document.getElementById('cleanSheetsChart').getContext('2d');
new Chart(cleanSheetsCtx, {
    type: 'bar',
    data: {
        labels: ['{{ h2h.home_team }}', '{{ h2h.away_team }}'],
        datasets: [{
            label: 'Clean Sheet %',
            data: [{{ h2h.home_clean_sheet_percentage }}, {{ h2h.away_clean_sheet_percentage }}],
            backgroundColor: ['#0d6efd', '#dc3545']
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        }
    }
});
</script>
{% endif %}
{% endblock %}