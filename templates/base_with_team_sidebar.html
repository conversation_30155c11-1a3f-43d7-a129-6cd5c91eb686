<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Football Database{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-futbol me-2"></i>Football Database
            </a>
            
            <div class="d-flex">
                <button class="navbar-toggler me-2" type="button" id="leagueSidebarToggle">
                    <i class="fas fa-trophy"></i>
                </button>
                
                <button class="navbar-toggler me-2" type="button" id="teamSidebarToggle">
                    <i class="fas fa-users"></i>
                </button>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
            </div>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('leagues') }}">
                            <i class="fas fa-trophy me-1"></i>Leagues
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('search') }}">
                            <i class="fas fa-search me-1"></i>Search
                        </a>
                    </li>
                </ul>
                
                <!-- Quick Search -->
                <form class="d-flex" action="{{ url_for('search') }}" method="GET">
                    <input class="form-control me-2" type="search" name="q" placeholder="Search teams, leagues..." 
                           value="{{ request.args.get('q', '') }}">
                    <button class="btn btn-outline-light" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>
    </nav>

    <!-- Sidebar Overlays for Mobile -->
    <div class="sidebar-overlay" id="leagueSidebarOverlay"></div>
    <div class="sidebar-overlay" id="teamSidebarOverlay"></div>

    <!-- Main Layout with Dual Sidebars -->
    <div class="container-fluid">
        <div class="row">
            <!-- League Sidebar -->
            <div class="col-lg-2 col-md-3 league-sidebar-container">
                <div class="sidebar">
                    <div class="sidebar-header">
                        <h6><i class="fas fa-trophy me-2"></i>Leagues</h6>
                        <div class="sidebar-search">
                            <input type="text" class="form-control form-control-sm" id="leagueSidebarSearch" 
                                   placeholder="Search leagues...">
                        </div>
                    </div>
                    
                    <div class="sidebar-content">
                        {% if sidebar_leagues %}
                            {% for country, leagues in sidebar_leagues.items() %}
                            <div class="league-group">
                                <div class="league-group-header" data-bs-toggle="collapse" 
                                     data-bs-target="#league-collapse-{{ country }}" aria-expanded="false">
                                    <i class="fas fa-chevron-right me-2"></i>
                                    {{ country.replace('_', ' ').title() }}
                                    <span class="badge bg-secondary ms-auto">{{ leagues|length }}</span>
                                </div>
                                <div class="collapse" id="league-collapse-{{ country }}">
                                    <div class="league-list">
                                        {% for league in leagues %}
                                        <a href="{{ url_for('league_detail', league_name=league) }}" 
                                           class="league-link {% if request.view_args and request.view_args.get('league_name') == league %}active{% endif %}"
                                           data-league="{{ league }}">
                                            {{ league.replace('_', ' ').title() }}
                                        </a>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Team Sidebar -->
            <div class="col-lg-2 col-md-3 team-sidebar-container">
                <div class="sidebar">
                    <div class="sidebar-header">
                        <h6><i class="fas fa-users me-2"></i>Teams</h6>
                        {% if league_name %}
                        <small class="text-muted">{{ league_name.replace('_', ' ').title() }}</small>
                        {% endif %}
                        <div class="sidebar-search">
                            <input type="text" class="form-control form-control-sm" id="teamSidebarSearch" 
                                   placeholder="Search teams...">
                        </div>
                    </div>
                    
                    <div class="sidebar-content" id="teamSidebarContent">
                        {% if teams and league_name %}
                            <div class="team-list">
                                {% for team in teams %}
                                <a href="{{ url_for('team_detail', league_name=league_name, team_name=team) }}" 
                                   class="team-link {% if request.view_args and request.view_args.get('team_name') == team %}active{% endif %}"
                                   data-team="{{ team }}"
                                   title="View {{ team }} details">
                                    <div class="team-link-content">
                                        <div class="team-name">
                                            <i class="fas fa-shield-alt me-2"></i>{{ team }}
                                        </div>
                                        <div class="team-actions">
                                            <i class="fas fa-chart-line text-muted" title="View stats"></i>
                                        </div>
                                    </div>
                                </a>
                                {% endfor %}
                            </div>
                            
                            <!-- Quick H2H Section -->
                            {% if request.view_args and (request.view_args.get('team_name') or (request.view_args.get('team1') and request.view_args.get('team2'))) %}
                            <div class="team-sidebar-section">
                                <div class="sidebar-section-header">
                                    <h6><i class="fas fa-balance-scale me-2"></i>Quick H2H</h6>
                                </div>
                                <div class="quick-h2h-list">
                                    {% if request.view_args.get('team_name') %}
                                        <!-- Team detail page - show H2H with all other teams -->
                                        {% for team in teams %}
                                            {% if team != request.view_args.get('team_name') %}
                                            <a href="{{ url_for('head_to_head', league_name=league_name, team1=request.view_args.get('team_name'), team2=team) }}" 
                                               class="quick-h2h-link"
                                               title="{{ request.view_args.get('team_name') }} vs {{ team }}">
                                                <i class="fas fa-vs me-2"></i>vs {{ team }}
                                            </a>
                                            {% endif %}
                                        {% endfor %}
                                    {% elif request.view_args.get('team1') and request.view_args.get('team2') %}
                                        <!-- H2H page - show H2H options for both teams -->
                                        {% set current_team1 = request.view_args.get('team1') %}
                                        {% set current_team2 = request.view_args.get('team2') %}
                                        
                                        <div class="h2h-team-section">
                                            <small class="text-muted">{{ current_team1 }} vs:</small>
                                            {% for team in teams %}
                                                {% if team != current_team1 %}
                                                <a href="{{ url_for('head_to_head', league_name=league_name, team1=current_team1, team2=team) }}" 
                                                   class="quick-h2h-link {% if team == current_team2 %}active{% endif %}"
                                                   title="{{ current_team1 }} vs {{ team }}">
                                                    <i class="fas fa-vs me-2"></i>vs {{ team }}
                                                </a>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                        
                                        <div class="h2h-team-section">
                                            <small class="text-muted">{{ current_team2 }} vs:</small>
                                            {% for team in teams %}
                                                {% if team != current_team2 %}
                                                <a href="{{ url_for('head_to_head', league_name=league_name, team1=current_team2, team2=team) }}" 
                                                   class="quick-h2h-link {% if team == current_team1 %}active{% endif %}"
                                                   title="{{ current_team2 }} vs {{ team }}">
                                                    <i class="fas fa-vs me-2"></i>vs {{ team }}
                                                </a>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}
                        {% else %}
                            <div class="text-muted text-center py-3">
                                <i class="fas fa-info-circle"></i>
                                <br><small>Select a league to view teams</small>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-lg-8 col-md-6 main-content-dual">
                <main class="mt-4">
                    {% block content %}{% endblock %}
                </main>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p class="text-muted mb-0">
                <i class="fas fa-database me-1"></i>
                Football Betting Database System | 
                <i class="fas fa-calendar me-1"></i>
                2024
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js for visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    <!-- Dual Sidebar functionality -->
    <script>
    // Mobile sidebar toggles
    document.getElementById('leagueSidebarToggle').addEventListener('click', function() {
        const sidebar = document.querySelector('.league-sidebar-container');
        const overlay = document.getElementById('leagueSidebarOverlay');
        
        sidebar.classList.toggle('show');
        overlay.classList.toggle('show');
        
        // Close team sidebar if open
        document.querySelector('.team-sidebar-container').classList.remove('show');
        document.getElementById('teamSidebarOverlay').classList.remove('show');
    });
    
    document.getElementById('teamSidebarToggle').addEventListener('click', function() {
        const sidebar = document.querySelector('.team-sidebar-container');
        const overlay = document.getElementById('teamSidebarOverlay');
        
        sidebar.classList.toggle('show');
        overlay.classList.toggle('show');
        
        // Close league sidebar if open
        document.querySelector('.league-sidebar-container').classList.remove('show');
        document.getElementById('leagueSidebarOverlay').classList.remove('show');
    });
    
    // Close sidebars when clicking overlays
    document.getElementById('leagueSidebarOverlay').addEventListener('click', function() {
        document.querySelector('.league-sidebar-container').classList.remove('show');
        this.classList.remove('show');
    });
    
    document.getElementById('teamSidebarOverlay').addEventListener('click', function() {
        document.querySelector('.team-sidebar-container').classList.remove('show');
        this.classList.remove('show');
    });
    
    // League sidebar search functionality
    document.getElementById('leagueSidebarSearch').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        const leagueGroups = document.querySelectorAll('.league-group');
        
        leagueGroups.forEach(group => {
            let hasVisibleLeagues = false;
            const links = group.querySelectorAll('.league-link');
            
            links.forEach(link => {
                const leagueName = link.dataset.league.toLowerCase();
                if (searchTerm === '' || leagueName.includes(searchTerm)) {
                    link.style.display = 'block';
                    hasVisibleLeagues = true;
                } else {
                    link.style.display = 'none';
                }
            });
            
            if (hasVisibleLeagues) {
                group.style.display = 'block';
                if (searchTerm !== '') {
                    const collapse = group.querySelector('.collapse');
                    if (collapse && !collapse.classList.contains('show')) {
                        new bootstrap.Collapse(collapse, {show: true});
                    }
                }
            } else {
                group.style.display = 'none';
            }
        });
    });
    
    // Team sidebar search functionality
    document.getElementById('teamSidebarSearch').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        const teamLinks = document.querySelectorAll('.team-link');
        const quickH2HLinks = document.querySelectorAll('.quick-h2h-link');
        
        teamLinks.forEach(link => {
            const teamName = link.dataset.team.toLowerCase();
            if (searchTerm === '' || teamName.includes(searchTerm)) {
                link.style.display = 'block';
            } else {
                link.style.display = 'none';
            }
        });
        
        // Also filter quick H2H links
        quickH2HLinks.forEach(link => {
            const linkText = link.textContent.toLowerCase();
            if (searchTerm === '' || linkText.includes(searchTerm)) {
                link.style.display = 'block';
            } else {
                link.style.display = 'none';
            }
        });
    });
    
    // Auto-expand current league's group
    document.addEventListener('DOMContentLoaded', function() {
        const activeLeagueLink = document.querySelector('.league-link.active');
        if (activeLeagueLink) {
            const group = activeLeagueLink.closest('.league-group');
            if (group) {
                const collapse = group.querySelector('.collapse');
                if (collapse) {
                    new bootstrap.Collapse(collapse, {show: true});
                }
            }
        }
    });
    
    // Toggle chevron icons for league groups
    document.querySelectorAll('.league-group-header').forEach(header => {
        header.addEventListener('click', function() {
            const icon = this.querySelector('.fas');
            const collapse = this.nextElementSibling;
            
            collapse.addEventListener('shown.bs.collapse', function() {
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-down');
            });
            
            collapse.addEventListener('hidden.bs.collapse', function() {
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-right');
            });
        });
    });
    
    // Close sidebars when clicking on links (mobile)
    document.querySelectorAll('.league-link, .team-link').forEach(link => {
        link.addEventListener('click', function() {
            if (window.innerWidth <= 991.98) {
                document.querySelector('.league-sidebar-container').classList.remove('show');
                document.querySelector('.team-sidebar-container').classList.remove('show');
                document.getElementById('leagueSidebarOverlay').classList.remove('show');
                document.getElementById('teamSidebarOverlay').classList.remove('show');
            }
        });
    });
    </script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>