{% extends "base.html" %}

{% block title %}{{ team1 }} vs {{ team2 }} - Prediction{% endblock %}

{% block extra_css %}
<style>
    .prediction-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    
    .probability-bar {
        background: rgba(255,255,255,0.2);
        border-radius: 10px;
        height: 30px;
        margin: 10px 0;
        position: relative;
        overflow: hidden;
    }
    
    .probability-fill {
        height: 100%;
        border-radius: 10px;
        transition: width 0.8s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: white;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }
    
    .prob-home { background: linear-gradient(90deg, #4CAF50, #45a049); }
    .prob-draw { background: linear-gradient(90deg, #FF9800, #f57c00); }
    .prob-away { background: linear-gradient(90deg, #2196F3, #1976d2); }
    
    .expected-goals {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    
    .team-stats {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .stat-item {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid #eee;
    }
    
    .stat-item:last-child {
        border-bottom: none;
    }
    
    .confidence-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: bold;
        margin: 0.5rem;
    }
    
    .confidence-high { background: #4CAF50; color: white; }
    .confidence-medium { background: #FF9800; color: white; }
    .confidence-low { background: #f44336; color: white; }
    
    .correct-scores {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .score-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        gap: 10px;
        margin-top: 1rem;
    }
    
    .score-item {
        background: rgba(255,255,255,0.2);
        border-radius: 8px;
        padding: 0.5rem;
        text-align: center;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item"><a href="/league/{{ league_name }}">{{ league_name.replace('_', ' ').title() }}</a></li>
            <li class="breadcrumb-item active">{{ team1 }} vs {{ team2 }} Prediction</li>
        </ol>
    </nav>

    <!-- Main Prediction Card -->
    <div class="prediction-card">
        <div class="row">
            <div class="col-md-12 text-center">
                <h1 class="mb-4">
                    <i class="fas fa-futbol me-2"></i>
                    {{ team1 }} vs {{ team2 }}
                </h1>
                <p class="lead">{{ league_name.replace('_', ' ').title() }} - Match Prediction</p>
            </div>
        </div>

        {% if predictions.main_predictions and predictions.main_predictions.three_way %}
        <div class="row mt-4">
            <div class="col-md-12">
                <h3 class="mb-3"><i class="fas fa-chart-bar me-2"></i>Match Outcome Probabilities</h3>
                
                {% set three_way = predictions.main_predictions.three_way %}
                {% set home_prob = (three_way.probabilities.Home * 100) | round(1) %}
                {% set draw_prob = (three_way.probabilities.Draw * 100) | round(1) %}
                {% set away_prob = (three_way.probabilities.Away * 100) | round(1) %}
                
                <div class="probability-bar">
                    <div class="probability-fill prob-home" style="width: {{ home_prob }}%">
                        {{ team1 }} Win: {{ home_prob }}%
                    </div>
                </div>
                
                <div class="probability-bar">
                    <div class="probability-fill prob-draw" style="width: {{ draw_prob }}%">
                        Draw: {{ draw_prob }}%
                    </div>
                </div>
                
                <div class="probability-bar">
                    <div class="probability-fill prob-away" style="width: {{ away_prob }}%">
                        {{ team2 }} Win: {{ away_prob }}%
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <h4>
                        <i class="fas fa-trophy me-2"></i>
                        Prediction: <strong>{{ three_way.prediction }}</strong>
                    </h4>
                </div>
            </div>
        </div>
        {% endif %}

        {% if predictions.expected_goals %}
        <div class="expected-goals">
            <h4><i class="fas fa-bullseye me-2"></i>Expected Goals</h4>
            <div class="row text-center">
                <div class="col-md-5">
                    <h2>{{ predictions.expected_goals.home | round(2) }}</h2>
                    <p>{{ team1 }}</p>
                </div>
                <div class="col-md-2">
                    <h3>-</h3>
                </div>
                <div class="col-md-5">
                    <h2>{{ predictions.expected_goals.away | round(2) }}</h2>
                    <p>{{ team2 }}</p>
                </div>
            </div>
        </div>
        {% endif %}

        {% if predictions.correct_scores %}
        <div class="correct-scores">
            <h4><i class="fas fa-calculator me-2"></i>Most Likely Scores</h4>
            <div class="score-grid">
                {% for score, prob in predictions.correct_scores.items() %}
                <div class="score-item">
                    <div><strong>{{ score }}</strong></div>
                    <div>{{ (prob * 100) | round(1) }}%</div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Additional Predictions -->
    {% if predictions.main_predictions %}
    <div class="row">
        {% for pred_type, pred_data in predictions.main_predictions.items() %}
        {% if pred_type != 'three_way' %}
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line me-2"></i>{{ pred_type.replace('_', ' ').title() }}</h5>
                </div>
                <div class="card-body">
                    <p><strong>Prediction:</strong> {{ pred_data.prediction }}</p>
                    {% if pred_data.probabilities %}
                    <div class="mt-3">
                        {% for outcome, prob in pred_data.probabilities.items() %}
                        <div class="d-flex justify-content-between">
                            <span>{{ outcome }}:</span>
                            <span><strong>{{ (prob * 100) | round(1) }}%</strong></span>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        {% endfor %}
    </div>
    {% endif %}

    <!-- Team Statistics Comparison -->
    <div class="row">
        <div class="col-md-6">
            <div class="team-stats">
                <h4><i class="fas fa-home me-2"></i>{{ team1 }} (Home)</h4>
                {% if team1_data.team_stats %}
                <div class="stat-item">
                    <span>Points Per Game:</span>
                    <span><strong>{{ team1_data.team_stats.points_per_game | round(2) }}</strong></span>
                </div>
                <div class="stat-item">
                    <span>Goals Scored/Game:</span>
                    <span><strong>{{ team1_data.team_stats.goals_scored_per_match_all | round(2) }}</strong></span>
                </div>
                <div class="stat-item">
                    <span>Goals Conceded/Game:</span>
                    <span><strong>{{ team1_data.team_stats.goals_conceded_per_match_all | round(2) }}</strong></span>
                </div>
                {% if team1_data.league_position %}
                <div class="stat-item">
                    <span>League Position:</span>
                    <span><strong>{{ team1_data.league_position }}</strong></span>
                </div>
                {% endif %}
                {% endif %}
                
                {% if team1_data.recent_form %}
                <h6 class="mt-3">Recent Form:</h6>
                <div class="d-flex">
                    {% for match in team1_data.recent_form[:5] %}
                    <span class="badge me-1 
                        {% if match.result == 'W' %}bg-success
                        {% elif match.result == 'D' %}bg-warning
                        {% else %}bg-danger{% endif %}">
                        {{ match.result }}
                    </span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="team-stats">
                <h4><i class="fas fa-plane me-2"></i>{{ team2 }} (Away)</h4>
                {% if team2_data.team_stats %}
                <div class="stat-item">
                    <span>Points Per Game:</span>
                    <span><strong>{{ team2_data.team_stats.points_per_game | round(2) }}</strong></span>
                </div>
                <div class="stat-item">
                    <span>Goals Scored/Game:</span>
                    <span><strong>{{ team2_data.team_stats.goals_scored_per_match_all | round(2) }}</strong></span>
                </div>
                <div class="stat-item">
                    <span>Goals Conceded/Game:</span>
                    <span><strong>{{ team2_data.team_stats.goals_conceded_per_match_all | round(2) }}</strong></span>
                </div>
                {% if team2_data.league_position %}
                <div class="stat-item">
                    <span>League Position:</span>
                    <span><strong>{{ team2_data.league_position }}</strong></span>
                </div>
                {% endif %}
                {% endif %}
                
                {% if team2_data.recent_form %}
                <h6 class="mt-3">Recent Form:</h6>
                <div class="d-flex">
                    {% for match in team2_data.recent_form[:5] %}
                    <span class="badge me-1 
                        {% if match.result == 'W' %}bg-success
                        {% elif match.result == 'D' %}bg-warning
                        {% else %}bg-danger{% endif %}">
                        {{ match.result }}
                    </span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-md-12 text-center">
            <a href="/league/{{ league_name }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to League
            </a>
            <a href="/predictions/{{ league_name }}" class="btn btn-primary me-2">
                <i class="fas fa-chart-line me-2"></i>More Predictions
            </a>
            <a href="/h2h/{{ league_name }}/{{ team1 }}/{{ team2 }}" class="btn btn-info">
                <i class="fas fa-vs me-2"></i>Head-to-Head Stats
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Add any interactive features here
document.addEventListener('DOMContentLoaded', function() {
    // Animate probability bars
    const bars = document.querySelectorAll('.probability-fill');
    bars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
});
</script>
{% endblock %}
