#!/usr/bin/env python3
"""
Test ML Pipeline - Single Match Prediction

A simplified version of the main ML pipeline that tests with just one league
and one match to verify the sophisticated ML system works.
"""

import sys
import os
import logging

# Add src to path
sys.path.append('src')

from data_loading import load_data, get_available_leagues
from feature_engineering import prepare_features
from model_training import train_model
from prediction import predict_match, save_predictions_to_excel
from analysis import perform_model_analysis
from utils import setup_logging, add_parent_dir_to_path

# Import league configs
try:
    from src.scrapers.config import LEAGUE_CONFIGS
except ImportError:
    print("Warning: Could not import LEAGUE_CONFIGS. Using empty dict.")
    LEAGUE_CONFIGS = {}

def setup_output_directories():
    """Create necessary output directories."""
    directories = [
        'data/processed',
        'data/processed/img',
        'analysis_output',
        'models'
    ]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def get_column_mappings():
    """Get column mappings for different data types."""
    return {
        "team_stats": {
            "points_per_game": "points_per_game",
            "goals_scored_per_match_home": "goals_scored_per_match_home",
            "goals_scored_per_match_away": "goals_scored_per_match_away",
            "goals_conceded_per_match_home": "goals_conceded_per_match_home",
            "goals_conceded_per_match_away": "goals_conceded_per_match_away",
            "total_home_wins": "total_home_wins",
            "total_home_played": "total_home_played",
            "total_away_wins": "total_away_wins",
            "total_away_played": "total_away_played",
            "total_home_draws": "total_home_draws",
            "total_away_draws": "total_away_draws",
            "ppg_last_8": "ppg_last_8",
            "avg_goals_scored_last_8": "avg_goals_scored_last_8",
            "avg_goals_conceded_last_8": "avg_goals_conceded_last_8",
        },
        "h2h_stats": {
            "team_a_win_percentage": "team_a_win_percentage",
            "team_b_win_percentage": "team_b_win_percentage",
            "draw_percentage": "draw_percentage",
            "total_matches": "total_matches",
            "team_a_goals": "team_a_goals",
            "team_b_goals": "team_b_goals",
            "btts_percentage": "btts_percentage",
        }
    }

def prepare_prediction_data(prepared_data):
    """Prepare X and y data for predictions."""
    columns_to_drop = [
        "result", "three_way", "double_chance",
        "over_under_1_5", "over_under_2_5", "over_under_3_5",
        "btts", "home_goals", "away_goals", "total_goals",
        "three_way_encoded", "double_chance_encoded",
        "over_under_1_5_encoded", "over_under_2_5_encoded",
        "over_under_3_5_encoded", "btts_encoded",
        "form_data_valid_str",
    ]

    X = prepared_data.drop(
        [col for col in columns_to_drop if col in prepared_data.columns],
        axis=1
    )

    y_dict = {
        "three_way": prepared_data["three_way"],
        "double_chance": prepared_data["double_chance"],
        "over_under_1_5": prepared_data["over_under_1_5"],
        "over_under_2_5": prepared_data["over_under_2_5"],
        "over_under_3_5": prepared_data["over_under_3_5"],
        "btts": prepared_data["btts"],
    }

    return X, y_dict

def test_single_match_prediction(league_name: str, home_team: str, away_team: str):
    """Test the ML pipeline with a single match."""
    
    print(f"\n🧪 TESTING ML PIPELINE")
    print("=" * 50)
    print(f"League: {league_name}")
    print(f"Match: {home_team} vs {away_team}")
    
    # Setup
    setup_logging()
    add_parent_dir_to_path()
    setup_output_directories()
    
    # Check if league config exists
    if league_name not in LEAGUE_CONFIGS:
        print(f"❌ No configuration found for {league_name}")
        return False
        
    league_config = LEAGUE_CONFIGS[league_name]
    if league_config is None:
        print(f"❌ League configuration is None for {league_name}")
        return False
    
    try:
        print(f"\n📊 Step 1: Loading data for {league_name}...")
        
        # Load and validate data
        data, league_config = load_data(league_name, league_config)
        if data is None:
            print(f"❌ Failed to load data for {league_name}")
            return False

        results, team_stats, league_stats, h2h_stats, league_table = data
        print(f"✅ Data loaded successfully")
        print(f"   - Results: {len(results)} matches")
        print(f"   - Team stats: {len(team_stats)} teams")
        print(f"   - H2H stats: {len(h2h_stats)} matchups")

        # Prepare team mappings
        if "TEAM_NAME_MAPPING" not in league_config:
            league_config["TEAM_NAME_MAPPING"] = {}

        # Update mapping with any new teams
        for team in team_stats["Team"].unique():
            if team not in league_config["TEAM_NAME_MAPPING"]:
                league_config["TEAM_NAME_MAPPING"][team] = team

        # Get column mappings
        mappings = get_column_mappings()
        combined_mapping = {**mappings["team_stats"], **mappings["h2h_stats"]}

        print(f"\n🔧 Step 2: Feature engineering...")
        
        # Prepare features
        prepared_data = prepare_features(
            results, team_stats, league_stats, h2h_stats, league_table,
            combined_mapping, league_config["TEAM_NAME_MAPPING"]
        )

        if prepared_data is None:
            print(f"❌ Feature preparation failed")
            return False
            
        print(f"✅ Features prepared: {len(prepared_data)} samples, {len(prepared_data.columns)} features")

        # Prepare X and y data
        X, y_dict = prepare_prediction_data(prepared_data)
        label_encoders = prepared_data.attrs.get("label_encoders", {})
        
        print(f"✅ Training data prepared: {len(X)} samples")

        # Validate data
        nan_columns = X.columns[X.isna().any()].tolist()
        if nan_columns:
            print(f"⚠️  Warning: NaN values found in columns: {nan_columns}")

        print(f"\n🤖 Step 3: Training models...")
        
        # Train models (this is the time-consuming part)
        models = train_model(X, y_dict, label_encoders)
        if not models:
            print(f"❌ No models trained")
            return False
            
        print(f"✅ Models trained: {list(models.keys())}")

        # Analyze models (optional - can be skipped for speed)
        print(f"\n📈 Step 4: Model analysis...")
        for pred_type, model_info in models.items():
            print(f"   Analyzing {pred_type} model...")
            try:
                perform_model_analysis(
                    model_info["model"],
                    X,
                    y_dict[pred_type],
                    model_info["feature_names"],
                    pred_type
                )
                print(f"   ✅ {pred_type} analysis complete")
            except Exception as e:
                print(f"   ⚠️  {pred_type} analysis failed: {e}")

        print(f"\n🎯 Step 5: Making prediction for {home_team} vs {away_team}...")
        
        # Check if teams exist
        if home_team not in team_stats["Team"].values:
            print(f"❌ Home team '{home_team}' not found in team stats")
            return False
        if away_team not in team_stats["Team"].values:
            print(f"❌ Away team '{away_team}' not found in team stats")
            return False

        # Get average goals per match
        avg_goals_per_match = float(
            league_stats[league_stats["Stat"] == "avg_goals_per_match"]["Value"].values[0]
        )

        # Make prediction
        pred_results, error_message, correct_scores = predict_match(
            models,
            home_team,
            away_team,
            team_stats,
            league_stats,
            h2h_stats,
            league_table,
            combined_mapping,
            models["three_way"]["feature_names"],
            avg_goals_per_match,
            label_encoders=label_encoders,
            bias_correction=0.05,
            log_features=True
        )

        if error_message:
            print(f"❌ Prediction error: {error_message}")
            return False

        print(f"✅ Prediction completed successfully!")
        
        # Display results
        print(f"\n🎯 PREDICTION RESULTS")
        print("=" * 50)
        
        main_predictions = pred_results.get("main_predictions", {})
        for pred_type, prediction in main_predictions.items():
            print(f"\n📊 {pred_type.replace('_', ' ').title()}:")
            if isinstance(prediction, dict) and "prediction" in prediction:
                print(f"   Prediction: {prediction['prediction']}")
                if "probabilities" in prediction:
                    probs = prediction["probabilities"]
                    for outcome, prob in probs.items():
                        print(f"   {outcome}: {prob:.1%}")

        # Save to Excel
        print(f"\n💾 Step 6: Saving results...")
        try:
            match_predictions = {f"{home_team} vs {away_team}": pred_results}
            save_predictions_to_excel(match_predictions, league_name)
            print(f"✅ Results saved to Excel")
        except Exception as e:
            print(f"⚠️  Excel save failed: {e}")

        print(f"\n🎉 ML PIPELINE TEST COMPLETED SUCCESSFULLY!")
        return True

    except Exception as e:
        print(f"❌ Error in ML pipeline test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function for testing."""
    
    # Test with a specific league and match
    # You can change these to test different leagues/matches
    
    TEST_LEAGUE = "ENGLAND_PREMIER_LEAGUE"
    TEST_HOME_TEAM = "Arsenal"
    TEST_AWAY_TEAM = "Chelsea"
    
    print("🧪 ML PIPELINE SINGLE MATCH TEST")
    print("=" * 50)
    print("This will test the sophisticated ML pipeline with:")
    print(f"   League: {TEST_LEAGUE}")
    print(f"   Match: {TEST_HOME_TEAM} vs {TEST_AWAY_TEAM}")
    print("\nThis includes:")
    print("   ✓ Data loading from CSV files")
    print("   ✓ Feature engineering")
    print("   ✓ Neural network + Random Forest training")
    print("   ✓ SHAP analysis")
    print("   ✓ Match prediction")
    print("   ✓ Excel output generation")
    
    success = test_single_match_prediction(TEST_LEAGUE, TEST_HOME_TEAM, TEST_AWAY_TEAM)
    
    if success:
        print(f"\n🎉 SUCCESS! The ML pipeline is working correctly.")
        print(f"Check the data/processed directory for Excel output.")
    else:
        print(f"\n❌ FAILED! There are issues with the ML pipeline.")
        
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
