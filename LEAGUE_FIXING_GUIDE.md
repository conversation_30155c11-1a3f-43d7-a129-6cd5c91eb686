# League Fixing Guide

This guide provides a systematic approach to fixing team mapping issues across all leagues in the football betting database.

## 📊 Overview

Based on our comprehensive analysis, we have:
- **314 total leagues** in the database
- **12,098 total teams** across all leagues
- **7,699 teams working** (63.6% success rate)
- **3,800 teams without stats** that need attention
- **0 broken canonical mappings** (our resolution system works!)

## 🎯 Fixing Strategy

### 1. **Prioritization Order**

Fix leagues in this order:
1. **High-impact leagues** with many teams and moderate success rates (70-90%)
2. **Leagues with config mappings available** (easier to fix)
3. **Alphabetical order** for systematic coverage
4. **Skip leagues with very low success rates** (0-20%) - likely missing data files

### 2. **Skip These League Types**

- **Youth leagues** (U21, U19, etc.) - often incomplete data
- **Lower divisions** with 0% success rates - missing CSV files
- **Leagues with <10 teams** and 0% success - likely data collection issues

## 🔧 Step-by-Step Fixing Process

### Step 1: Analyze the League

```python
# Check the team_mapping_issues_report.md for:
# - Current success rate
# - Number of teams with issues
# - Available config mappings
# - Specific canonical mapping problems
```

### Step 2: Examine the CSV File

```bash
# Check if the league has a team stats CSV file
ls data/raw/LEAGUE_NAME/LEAGUE_NAME_team_stats.csv

# Look at the team names in the CSV
head -20 data/raw/LEAGUE_NAME/LEAGUE_NAME_team_stats.csv
```

### Step 3: Check Config Mappings

```python
# Look at the league config file
cat src/scrapers/league_configs/LEAGUE_NAME.py

# Check for TEAM_NAME_MAPPING dictionary
# This contains real-world team name changes/mappings
```

### Step 4: Identify the Issues

Common patterns:
1. **Historical team names** → **Current team names** (e.g., Artsakh → Noah)
2. **Full official names** → **Short names** (e.g., "KF Tirana" → "Tirana")
3. **Different formatting** → **CSV format** (e.g., "FC Barcelona" → "Barcelona")

### Step 5: Apply Fixes

Use this template script:

```python
#!/usr/bin/env python3
"""
Fix [LEAGUE_NAME] Team Mappings
"""

import sys
sys.path.append('src')
from database.football_db import get_database

def fix_league_mappings():
    with get_database() as db:
        # Define specific mappings based on analysis
        mappings = [
            ("old_team_name", "current_team_name", "reason"),
            # Add more mappings here
        ]
        
        fixes_applied = 0
        
        for old_team, target_team, reason in mappings:
            print(f"🔧 Fixing: {old_team} → {target_team}")
            print(f"   Reason: {reason}")
            
            # Get team IDs
            old_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (old_team, "LEAGUE_NAME"))
            
            target_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (target_team, "LEAGUE_NAME"))
            
            if old_id and target_id:
                # Verify target has stats
                target_stats = db.get_team_stats(target_team, "LEAGUE_NAME")
                if not target_stats.empty:
                    # Apply the mapping
                    db.conn.execute(
                        'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                        (target_id, old_id)
                    )
                    print(f"   ✅ Applied mapping")
                    fixes_applied += 1
                else:
                    print(f"   ⚠️  Target team has no stats")
            else:
                print(f"   ❌ Could not find team IDs")
        
        if fixes_applied > 0:
            db.conn.commit()
            print(f"💾 Committed {fixes_applied} fixes")
        
        return fixes_applied

if __name__ == "__main__":
    fix_league_mappings()
```

## 🔍 Common Mapping Patterns

### Pattern 1: Prefix/Suffix Removal
```
"KF Tirana" → "Tirana"
"FC Barcelona" → "Barcelona"  
"Real Madrid CF" → "Real Madrid"
```

### Pattern 2: Historical Team Names
```
"Artsakh" → "Noah" (team rebrand in 2022)
"FC Avan Academy" → "Ararat Armenia" (merger/rename)
```

### Pattern 3: Official vs Common Names
```
"Brighton & Hove Albion FC" → "Brighton"
"Deportivo Alavés" → "Alaves"
```

### Pattern 4: Formatting Differences
```
"Qarabağ FK" → "Qarabag"
"İstanbul Başakşehir" → "Istanbul Basaksehir"
```

## 📋 League Priority List

Based on the analysis report, fix these leagues first:

### High Priority (Good success rate, many teams)
1. **ALBANIA_SUPERLIGA** - 24/29 teams (82.8%) - Config available
2. **ARGENTINA_PRIMERA_DIVISION** - Check success rate
3. **AUSTRALIA_A_LEAGUE** - Check success rate
4. **AUSTRIA_BUNDESLIGA** - Check success rate
5. **BELGIUM_FIRST_DIVISION_A** - Check success rate

### Medium Priority (Moderate success rate)
6. **BRAZIL_SERIE_A** - Check success rate
7. **BULGARIA_FIRST_LEAGUE** - Check success rate
8. **CROATIA_1_HNL** - Check success rate
9. **CZECH_REPUBLIC_FIRST_LEAGUE** - Check success rate
10. **DENMARK_SUPERLIGA** - Check success rate

### Skip These (Low success rates, likely missing data)
- **ALGERIA_U21** - 0/16 teams (0%)
- **ARGENTINA_U20** - Low success rate
- **AUSTRALIA_YOUTH_LEAGUES** - Incomplete data
- Any league with <20% success rate and <10 teams

## 🧪 Testing Your Fixes

After applying fixes, always test:

```python
# Test specific teams that were problematic
test_teams = ["problematic_team_1", "problematic_team_2"]

with get_database() as db:
    for team in test_teams:
        stats = db.get_team_stats(team, "LEAGUE_NAME")
        canonical = db._get_canonical_team_name(team, "LEAGUE_NAME")
        
        if not stats.empty:
            ppg = stats.iloc[0].get('points_per_game', 'N/A')
            print(f"✅ {team} → {canonical} (PPG: {ppg})")
        else:
            print(f"❌ {team} → {canonical} (no stats)")
```

## 📊 Measuring Success

Track improvement:
- **Before**: X/Y teams working (Z%)
- **After**: A/Y teams working (B%)
- **Improvement**: +C teams (+D percentage points)

## 🎯 Success Examples

### Armenia Premier League ✅
- **Before**: 20/26 teams (76.9%)
- **After**: 24/26 teams (92.3%)
- **Improvement**: +4 teams (+15.4 percentage points)
- **Key fixes**: Artsakh→Noah, FC Avan Academy→Ararat Armenia, FC Banants→Urartu

## 🚀 Automation Opportunities

For leagues with config mappings:
1. **Extract config mappings** automatically
2. **Apply standard mapping patterns** (remove KF/FC prefixes)
3. **Use similarity matching** for close team names
4. **Batch process** multiple leagues

## 📝 Documentation

For each league fixed:
1. **Record the mappings applied**
2. **Note the improvement achieved**
3. **Document any special cases**
4. **Update the success rate**

## ⚠️ Common Pitfalls

1. **Don't map teams that don't exist** in the database
2. **Always verify the target team has stats** before mapping
3. **Check for reverse mappings** (sometimes config is backwards)
4. **Commit changes** after successful mappings
5. **Test the web application** after fixes

## 🎉 Goal

Achieve **90%+ success rate** across all major leagues by systematically applying these fixes!

## 🚀 Quick Start

1. **Check the report**: Look at `team_mapping_issues_report.md`
2. **Copy the template**: `cp league_fix_template.py fix_LEAGUE_NAME.py`
3. **Update constants**: Change `LEAGUE_NAME` and `SPECIFIC_MAPPINGS`
4. **Run the script**: `python3 fix_LEAGUE_NAME.py`
5. **Test in web app**: Visit team pages to verify fixes work

## 📋 Quick Reference - Common Patterns

```python
# Pattern 1: Remove prefixes
("KF Tirana", "Tirana", "Remove KF prefix"),
("FC Barcelona", "Barcelona", "Remove FC prefix"),

# Pattern 2: Historical names
("Artsakh", "Noah", "Team rebrand 2022"),
("FC Avan Academy", "Ararat Armenia", "Team merger"),

# Pattern 3: Official vs short names
("Brighton & Hove Albion FC", "Brighton", "Short name used in CSV"),
("Real Madrid CF", "Real Madrid", "Common name format"),

# Pattern 4: Formatting differences
("İstanbul Başakşehir", "Istanbul Basaksehir", "ASCII formatting"),
```

## 🎯 Success Metrics

Track these for each league:
- **Teams fixed**: Number of new working teams
- **Success rate improvement**: Percentage point increase
- **Total working teams**: Final count
- **Remaining issues**: Teams still needing attention
