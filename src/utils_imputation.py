
def impute_missing_features(X):
    """Impute missing values in features."""
    from sklearn.impute import SimpleImputer
    import pandas as pd
    
    # Separate numeric and categorical columns
    numeric_cols = X.select_dtypes(include=[np.number]).columns
    categorical_cols = X.select_dtypes(exclude=[np.number]).columns
    
    X_imputed = X.copy()
    
    # Impute numeric columns with median
    if len(numeric_cols) > 0:
        numeric_imputer = SimpleImputer(strategy='median')
        X_imputed[numeric_cols] = numeric_imputer.fit_transform(X_imputed[numeric_cols])
    
    # Impute categorical columns with most frequent
    if len(categorical_cols) > 0:
        categorical_imputer = SimpleImputer(strategy='most_frequent')
        X_imputed[categorical_cols] = categorical_imputer.fit_transform(X_imputed[categorical_cols])
    
    return X_imputed
