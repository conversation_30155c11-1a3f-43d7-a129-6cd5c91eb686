#!/usr/bin/env python3
"""
Football Database Utility Module

This module provides easy access to the centralized SQLite database,
replacing the CSV file system with efficient database operations.
"""

import sqlite3
import pandas as pd
import os
from typing import List, Dict, Optional, Tuple
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class FootballDatabase:
    """Main database interface for football betting data."""
    
    def __init__(self, db_path='data/football_betting.db'):
        self.db_path = db_path
        self.conn = None
        
    def connect(self):
        """Create database connection."""
        if not os.path.exists(self.db_path):
            raise FileNotFoundError(f"Database not found: {self.db_path}. Run create_database.py first.")
        
        self.conn = sqlite3.connect(self.db_path)
        self.conn.row_factory = sqlite3.Row  # Enable column access by name
        
    def disconnect(self):
        """Close database connection."""
        if self.conn:
            self.conn.close()
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()
    
    def execute_query(self, query: str, params: tuple = ()) -> pd.DataFrame:
        """Execute query and return results as DataFrame."""
        if not self.conn:
            self.connect()
        
        return pd.read_sql_query(query, self.conn, params=params)
    
    def execute_scalar(self, query: str, params: tuple = ()):
        """Execute query and return single value."""
        if not self.conn:
            self.connect()
        
        cursor = self.conn.execute(query, params)
        result = cursor.fetchone()
        return result[0] if result else None
    
    # League-related methods
    def get_leagues(self) -> pd.DataFrame:
        """Get all leagues."""
        return self.execute_query("SELECT * FROM leagues ORDER BY league_name")
    
    def get_league_id(self, league_name: str) -> Optional[int]:
        """Get league ID by name."""
        return self.execute_scalar(
            "SELECT league_id FROM leagues WHERE league_name = ?",
            (league_name,)
        )
    
    def get_league_stats(self, league_name: str) -> pd.DataFrame:
        """Get statistics for a specific league."""
        return self.execute_query('''
            SELECT ls.stat_name, ls.stat_value
            FROM league_stats ls
            JOIN leagues l ON ls.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY ls.stat_name
        ''', (league_name,))
    
    # Team-related methods
    def get_teams(self, league_name: str = None) -> pd.DataFrame:
        """Get teams, optionally filtered by league."""
        if league_name:
            return self.execute_query('''
                SELECT DISTINCT canonical.team_id, canonical.team_name, l.league_name
                FROM teams t
                JOIN teams canonical ON t.canonical_team_id = canonical.team_id
                JOIN leagues l ON canonical.league_id = l.league_id
                WHERE l.league_name = ?
                ORDER BY canonical.team_name
            ''', (league_name,))
        else:
            return self.execute_query('''
                SELECT DISTINCT canonical.team_id, canonical.team_name, l.league_name
                FROM teams t
                JOIN teams canonical ON t.canonical_team_id = canonical.team_id
                JOIN leagues l ON canonical.league_id = l.league_id
                ORDER BY l.league_name, canonical.team_name
            ''')
    
    def get_team_stats(self, team_name: str, league_name: str = None) -> pd.DataFrame:
        """Get statistics for a specific team."""
        if league_name:
            return self.execute_query('''
                SELECT ts.*, t.team_name, l.league_name
                FROM team_stats ts
                JOIN teams t ON ts.team_id = t.team_id
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (team_name, league_name))
        else:
            return self.execute_query('''
                SELECT ts.*, t.team_name, l.league_name
                FROM team_stats ts
                JOIN teams t ON ts.team_id = t.team_id
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ?
            ''', (team_name,))
    
    def get_league_table(self, league_name: str) -> pd.DataFrame:
        """Get league table/standings for a specific league."""
        return self.execute_query('''
            SELECT 
                lt.position,
                t.team_name,
                lt.matches_played as MP,
                lt.wins as W,
                lt.draws as D,
                lt.losses as L,
                lt.goals_for as GF,
                lt.goals_against as GA,
                lt.goal_difference as GD,
                lt.points as Pts
            FROM league_table lt
            JOIN teams t ON lt.team_id = t.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY lt.position
        ''', (league_name,))
    
    # Match-related methods
    def get_match_results(self, league_name: str, limit: int = None) -> pd.DataFrame:
        """Get match results for a specific league."""
        query = '''
            SELECT 
                mr.match_date,
                ht.team_name as home_team,
                mr.home_score,
                mr.away_score,
                at.team_name as away_team,
                mr.result_code
            FROM match_results mr
            JOIN teams ht ON mr.home_team_id = ht.team_id
            JOIN teams at ON mr.away_team_id = at.team_id
            JOIN leagues l ON mr.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY mr.match_date DESC
        '''
        
        if limit:
            query += f" LIMIT {limit}"
        
        return self.execute_query(query, (league_name,))
    
    def get_team_matches(self, team_name: str, league_name: str = None, limit: int = None) -> pd.DataFrame:
        """Get matches for a specific team."""
        query = '''
            SELECT 
                mr.match_date,
                ht.team_name as home_team,
                mr.home_score,
                mr.away_score,
                at.team_name as away_team,
                mr.result_code,
                CASE 
                    WHEN ht.team_name = ? THEN 'Home'
                    WHEN at.team_name = ? THEN 'Away'
                END as venue
            FROM match_results mr
            JOIN teams ht ON mr.home_team_id = ht.team_id
            JOIN teams at ON mr.away_team_id = at.team_id
            JOIN leagues l ON mr.league_id = l.league_id
            WHERE (ht.team_name = ? OR at.team_name = ?)
        '''
        
        params = [team_name, team_name, team_name, team_name]
        
        if league_name:
            query += " AND l.league_name = ?"
            params.append(league_name)
        
        query += " ORDER BY mr.match_date DESC"
        
        if limit:
            query += f" LIMIT {limit}"
        
        return self.execute_query(query, tuple(params))
    
    # Head-to-head methods
    def get_head_to_head_stats(self, team1: str, team2: str, league_name: str = None) -> pd.DataFrame:
        """Get head-to-head statistics between two teams using canonical team names."""

        # First, resolve input team names to their canonical names
        canonical_team1 = self._get_canonical_team_name(team1, league_name)
        canonical_team2 = self._get_canonical_team_name(team2, league_name)

        if not canonical_team1 or not canonical_team2:
            # If we can't resolve canonical names, return empty DataFrame
            return pd.DataFrame()

        query = '''
            SELECT
                h2h.*,
                ht_canonical.team_name as home_team,
                at_canonical.team_name as away_team,
                l.league_name
            FROM head_to_head_stats h2h
            JOIN teams ht ON h2h.home_team_id = ht.team_id
            JOIN teams at ON h2h.away_team_id = at.team_id
            JOIN teams ht_canonical ON ht.canonical_team_id = ht_canonical.team_id
            JOIN teams at_canonical ON at.canonical_team_id = at_canonical.team_id
            JOIN leagues l ON h2h.league_id = l.league_id
            WHERE ((ht_canonical.team_name = ? AND at_canonical.team_name = ?) OR
                   (ht_canonical.team_name = ? AND at_canonical.team_name = ?))
        '''

        params = [canonical_team1, canonical_team2, canonical_team2, canonical_team1]

        if league_name:
            query += " AND l.league_name = ?"
            params.append(league_name)

        return self.execute_query(query, tuple(params))

    def _get_canonical_team_name(self, team_name: str, league_name: str = None) -> str:
        """Get the canonical name for a team."""
        query = '''
            SELECT t2.team_name as canonical_name
            FROM teams t1
            JOIN teams t2 ON t1.canonical_team_id = t2.team_id
            JOIN leagues l ON t1.league_id = l.league_id
            WHERE t1.team_name = ?
        '''

        params = [team_name]

        if league_name:
            query += " AND l.league_name = ?"
            params.append(league_name)

        result = self.execute_query(query, tuple(params))

        if not result.empty:
            return result.iloc[0]['canonical_name']
        else:
            # If no canonical mapping found, return the original name
            return team_name
    
    def get_all_h2h_stats(self, league_name: str) -> pd.DataFrame:
        """Get all head-to-head statistics for a league."""
        return self.execute_query('''
            SELECT 
                h2h.*,
                ht.team_name as home_team,
                at.team_name as away_team
            FROM head_to_head_stats h2h
            JOIN teams ht ON h2h.home_team_id = ht.team_id
            JOIN teams at ON h2h.away_team_id = at.team_id
            JOIN leagues l ON h2h.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY ht.team_name, at.team_name
        ''', (league_name,))

    def get_recent_matches(self, league_name: str, limit: int = 10) -> pd.DataFrame:
        """Get recent matches from a league."""
        return self.execute_query('''
            SELECT DISTINCT
                mr.match_date,
                ht.team_name as home_team,
                mr.home_score,
                mr.away_score,
                at.team_name as away_team
            FROM match_results mr
            JOIN teams ht ON mr.home_team_id = ht.team_id
            JOIN teams at ON mr.away_team_id = at.team_id
            JOIN leagues l ON mr.league_id = l.league_id
            WHERE l.league_name = ?
            AND mr.home_score IS NOT NULL AND mr.away_score IS NOT NULL
            ORDER BY
                -- Football season order: Aug-Dec (early season), Jan-May (late season)
                -- Late season months (Jan-May) should come first as they are more recent
                CASE
                    WHEN substr(mr.match_date, instr(mr.match_date, ' ') + 1, 3) IN ('Jan', 'Feb', 'Mar', 'Apr', 'May') THEN 1
                    WHEN substr(mr.match_date, instr(mr.match_date, ' ') + 1, 3) IN ('Aug', 'Sep', 'Oct', 'Nov', 'Dec') THEN 2
                    ELSE 3
                END,
                -- Within each season period, sort by month order
                CASE substr(mr.match_date, instr(mr.match_date, ' ') + 1, 3)
                    WHEN 'May' THEN 1
                    WHEN 'Apr' THEN 2
                    WHEN 'Mar' THEN 3
                    WHEN 'Feb' THEN 4
                    WHEN 'Jan' THEN 5
                    WHEN 'Dec' THEN 6
                    WHEN 'Nov' THEN 7
                    WHEN 'Oct' THEN 8
                    WHEN 'Sep' THEN 9
                    WHEN 'Aug' THEN 10
                    ELSE 11
                END,
                -- Sort by day within the month (descending for most recent first)
                CAST(substr(mr.match_date, 1, instr(mr.match_date, ' ') - 1) AS INTEGER) DESC
            LIMIT ?
        ''', (league_name, limit))

    # Analysis methods
    def get_team_form(self, team_name: str, league_name: str, last_n_matches: int = 5) -> pd.DataFrame:
        """Get recent form for a team with proper date sorting."""
        return self.execute_query('''
            SELECT DISTINCT
                mr.match_date,
                ht.team_name as home_team,
                mr.home_score,
                mr.away_score,
                at.team_name as away_team,
                CASE
                    WHEN ht.team_name = ? THEN 'Home'
                    WHEN at.team_name = ? THEN 'Away'
                END as venue,
                CASE
                    WHEN (ht.team_name = ? AND mr.home_score > mr.away_score) OR
                         (at.team_name = ? AND mr.away_score > mr.home_score) THEN 'W'
                    WHEN mr.home_score = mr.away_score THEN 'D'
                    ELSE 'L'
                END as result
            FROM match_results mr
            JOIN teams ht ON mr.home_team_id = ht.team_id
            JOIN teams at ON mr.away_team_id = at.team_id
            JOIN leagues l ON mr.league_id = l.league_id
            WHERE (ht.team_name = ? OR at.team_name = ?) AND l.league_name = ?
            AND mr.home_score IS NOT NULL AND mr.away_score IS NOT NULL
            ORDER BY
                -- Football season order: Aug-Dec (early season), Jan-May (late season)
                -- Late season months (Jan-May) should come first as they are more recent
                CASE
                    WHEN substr(mr.match_date, instr(mr.match_date, ' ') + 1, 3) IN ('Jan', 'Feb', 'Mar', 'Apr', 'May') THEN 1
                    WHEN substr(mr.match_date, instr(mr.match_date, ' ') + 1, 3) IN ('Aug', 'Sep', 'Oct', 'Nov', 'Dec') THEN 2
                    ELSE 3
                END,
                -- Within each season period, sort by month order
                CASE substr(mr.match_date, instr(mr.match_date, ' ') + 1, 3)
                    WHEN 'May' THEN 1
                    WHEN 'Apr' THEN 2
                    WHEN 'Mar' THEN 3
                    WHEN 'Feb' THEN 4
                    WHEN 'Jan' THEN 5
                    WHEN 'Dec' THEN 6
                    WHEN 'Nov' THEN 7
                    WHEN 'Oct' THEN 8
                    WHEN 'Sep' THEN 9
                    WHEN 'Aug' THEN 10
                    ELSE 11
                END,
                -- Sort by day within the month (descending for most recent first)
                CAST(substr(mr.match_date, 1, instr(mr.match_date, ' ') - 1) AS INTEGER) DESC
            LIMIT ?
        ''', (team_name, team_name, team_name, team_name, team_name, team_name, league_name, last_n_matches))
    
    def get_league_top_scorers(self, league_name: str, limit: int = 10) -> pd.DataFrame:
        """Get top scoring teams in a league."""
        return self.execute_query('''
            SELECT 
                t.team_name,
                ts.goals_scored_all,
                ts.goals_scored_per_match_all,
                ts.total_played
            FROM team_stats ts
            JOIN teams t ON ts.team_id = t.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY ts.goals_scored_all DESC
            LIMIT ?
        ''', (league_name, limit))
    
    def get_database_summary(self) -> Dict:
        """Get summary statistics about the database."""
        summary = {}
        
        # Count records in each table
        tables = ['leagues', 'teams', 'league_stats', 'team_stats', 
                 'league_table', 'match_results', 'head_to_head_stats']
        
        for table in tables:
            count = self.execute_scalar(f'SELECT COUNT(*) FROM {table}')
            summary[table] = count
        
        # Database file size
        if os.path.exists(self.db_path):
            summary['database_size_mb'] = os.path.getsize(self.db_path) / (1024 * 1024)
        
        # League with most teams
        league_teams = self.execute_query('''
            SELECT l.league_name, COUNT(t.team_id) as team_count
            FROM leagues l
            LEFT JOIN teams t ON l.league_id = t.league_id
            GROUP BY l.league_id, l.league_name
            ORDER BY team_count DESC
            LIMIT 1
        ''')
        
        if not league_teams.empty:
            summary['most_teams_league'] = league_teams.iloc[0]['league_name']
            summary['most_teams_count'] = league_teams.iloc[0]['team_count']
        
        return summary

def normalize_team_name(team_name: str, league_name: str = None, context: str = 'database') -> str:
    """
    Normalize team names by querying the database for the canonical name.
    If a canonical team ID is present, we use it to fetch the correct stats.
    """
    if not team_name:
        return team_name

    with get_database() as db:
        # Check if a canonical ID exists for the given team name
        canonical_id = db.execute_scalar("""
            SELECT canonical_team_id
            FROM teams
            WHERE team_name = ? AND league_id = (
                SELECT league_id FROM leagues WHERE league_name = ?
            )
        """, (team_name, league_name))

        # If a canonical ID is found, get the canonical team name
        if canonical_id:
            canonical_name = db.execute_scalar(
                "SELECT team_name FROM teams WHERE team_id = ?",
                (canonical_id,)
            )
            if canonical_name:
                return canonical_name

    # Fallback to original name if no canonical mapping is found
    return team_name

# Convenience functions for common operations
def get_database() -> FootballDatabase:
    """Get a database instance."""
    return FootballDatabase()

def query_to_dataframe(db: FootballDatabase, query: str, params: tuple = ()) -> pd.DataFrame:
    """Execute query and return DataFrame."""
    return db.execute_query(query, params)

def get_available_leagues() -> List[str]:
    """Get list of available leagues."""
    with get_database() as db:
        leagues_df = db.get_leagues()
        return leagues_df['league_name'].tolist()

def get_league_teams(league_name: str) -> List[str]:
    """Get list of teams in a league."""
    with get_database() as db:
        teams_df = db.get_teams(league_name)
        return teams_df['team_name'].tolist()

# Query builder class for complex queries
class QueryBuilder:
    """Helper class to build complex SQL queries."""
    
    @staticmethod
    def get_team_performance_query(league_name: str, min_matches: int = 5) -> str:
        """Build query for team performance analysis."""
        return '''
            SELECT 
                t.team_name,
                ts.total_played,
                ts.total_wins,
                ts.total_draws,
                ts.total_losses,
                ts.points_per_game,
                ts.goals_scored_per_match_all,
                ts.goals_conceded_per_match_all,
                (ts.goals_scored_per_match_all - ts.goals_conceded_per_match_all) as goal_difference_per_match,
                lt.position,
                lt.points
            FROM team_stats ts
            JOIN teams t ON ts.team_id = t.team_id
            JOIN leagues l ON t.league_id = l.league_id
            LEFT JOIN league_table lt ON t.team_id = lt.team_id
            WHERE l.league_name = ? AND ts.total_played >= ?
            ORDER BY lt.position
        '''
    
    @staticmethod
    def get_h2h_summary_query(league_name: str) -> str:
        """Build query for H2H summary statistics."""
        return '''
            SELECT 
                COUNT(*) as total_matchups,
                AVG(total_matches) as avg_matches_per_h2h,
                AVG(home_win_percentage) as avg_home_win_pct,
                AVG(away_win_percentage) as avg_away_win_pct,
                AVG(draw_percentage) as avg_draw_pct,
                AVG(btts_percentage) as avg_btts_pct
            FROM head_to_head_stats h2h
            JOIN leagues l ON h2h.league_id = l.league_id
            WHERE l.league_name = ?
        '''
    
    @staticmethod
    def get_recent_form_query(team_name: str, league_name: str, days: int = 30) -> str:
        """Build query for recent team form."""
        return '''
            SELECT 
                COUNT(*) as matches_played,
                SUM(CASE 
                    WHEN (ht.team_name = ? AND mr.home_score > mr.away_score) OR 
                         (at.team_name = ? AND mr.away_score > mr.home_score) THEN 1 
                    ELSE 0 
                END) as wins,
                SUM(CASE WHEN mr.home_score = mr.away_score THEN 1 ELSE 0 END) as draws,
                SUM(CASE 
                    WHEN (ht.team_name = ? AND mr.home_score < mr.away_score) OR 
                         (at.team_name = ? AND mr.away_score < mr.home_score) THEN 1 
                    ELSE 0 
                END) as losses
            FROM match_results mr
            JOIN teams ht ON mr.home_team_id = ht.team_id
            JOIN teams at ON mr.away_team_id = at.team_id
            JOIN leagues l ON mr.league_id = l.league_id
            WHERE (ht.team_name = ? OR at.team_name = ?) 
            AND l.league_name = ?
            AND mr.home_score IS NOT NULL 
            AND mr.away_score IS NOT NULL
            AND date(mr.match_date) >= date('now', '-{} days')
        '''.format(days)