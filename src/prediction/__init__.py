"""
Prediction package for the betting project.
This package handles all prediction functionality including match predictions,
probability calculations, score predictions, and result analysis.
"""

# Import modules conditionally to avoid TensorFlow dependency issues
try:
    from .core import predict_match
    FULL_PREDICTION_AVAILABLE = True
except ImportError:
    FULL_PREDICTION_AVAILABLE = False

try:
    from .expected_goals import calculate_expected_goals
except ImportError:
    pass

try:
    from .probabilities import (
        blend_predictions,
        calibrate_probabilities,
        calculate_over_under_probs,
        calculate_double_chance_probabilities
    )
except ImportError:
    pass

try:
    from .scores import (
        predict_correct_scores,
        dixon_coles_adjustment,
        calculate_dixon_coles_probabilities
    )
except ImportError:
    pass

try:
    from .analysis import (
        analyze_prediction_confidence,
        assess_prediction_risk,
        generate_prediction_summary,
        log_prediction_details
    )
except ImportError:
    pass

try:
    from .validation import (
        validate_predictions,
        calibrate_double_chance_probabilities
    )
except ImportError:
    pass

try:
    from .excel_output import save_predictions_to_excel
except ImportError:
    pass

__all__ = [
    # Core prediction functionality
    'predict_match',
    
    # Expected goals calculation
    'calculate_expected_goals',
    
    # Probability calculations
    'blend_predictions',
    'calibrate_probabilities',
    'calculate_over_under_probs',
    'calculate_double_chance_probabilities',
    
    # Score predictions
    'predict_correct_scores',
    'dixon_coles_adjustment',
    'calculate_dixon_coles_probabilities',
    
    # Analysis functionality
    'analyze_prediction_confidence',
    'assess_prediction_risk',
    'generate_prediction_summary',
    'log_prediction_details',
    
    # Validation
    'validate_predictions',
    'calibrate_double_chance_probabilities',
    
    # Excel output
    'save_predictions_to_excel'
]

# Version of the prediction package
__version__ = '1.0.0'
