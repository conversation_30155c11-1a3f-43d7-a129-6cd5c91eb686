#!/usr/bin/env python3
"""
Simple Prediction Service

A lightweight prediction service that provides basic predictions without
requiring TensorFlow or complex ML models. This serves as a demonstration
and fallback for the web application integration.
"""

import logging
import pandas as pd
import numpy as np
import random
import sys
from typing import Dict, Tuple, Optional, Any, List
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from database.football_db import get_database, normalize_team_name

logger = logging.getLogger(__name__)

class SimplePredictionService:
    """
    Simple prediction service that uses basic statistical analysis
    to provide match predictions without requiring complex ML models.
    """
    
    def __init__(self, db_path='data/football_betting.db'):
        """Initialize the simple prediction service."""
        self.db_path = db_path
        
    def get_available_leagues(self) -> List[str]:
        """Get list of leagues available for predictions."""
        try:
            with get_database() as db:
                leagues = db.execute_query("SELECT DISTINCT league_name FROM leagues ORDER BY league_name")
                return leagues['league_name'].tolist() if not leagues.empty else []
        except Exception as e:
            logger.error(f"Error getting available leagues: {e}")
            return []
    
    def predict_match(
        self, 
        home_team: str, 
        away_team: str, 
        league_name: str,
        use_cached_models: bool = True
    ) -> Tuple[Dict[str, Any], Optional[str]]:
        """
        Predict the outcome of a match using simple statistical analysis.
        
        Args:
            home_team: Name of the home team
            away_team: Name of the away team
            league_name: Name of the league
            use_cached_models: Ignored in simple service
            
        Returns:
            Tuple of (predictions_dict, error_message)
        """
        try:
            logger.info(f"Simple prediction: {home_team} vs {away_team} in {league_name}")
            
            # Normalize team names
            home_team = normalize_team_name(home_team, league_name)
            away_team = normalize_team_name(away_team, league_name)
            
            with get_database() as db:
                # Get team statistics
                home_stats = db.get_team_stats(home_team, league_name)
                away_stats = db.get_team_stats(away_team, league_name)
                
                if home_stats.empty or away_stats.empty:
                    return {}, f"Team statistics not found for {home_team} or {away_team}"
                
                home_data = home_stats.iloc[0]
                away_data = away_stats.iloc[0]
                
                # Get head-to-head data
                h2h_data = db.get_head_to_head_stats(home_team, away_team, league_name)
                
                # Calculate predictions
                predictions = self._calculate_simple_predictions(
                    home_data, away_data, h2h_data, home_team, away_team, league_name
                )
                
                return predictions, None
                
        except Exception as e:
            logger.error(f"Error in simple prediction: {e}")
            return {}, f"Prediction error: {str(e)}"
    
    def _calculate_simple_predictions(
        self,
        home_data: pd.Series,
        away_data: pd.Series,
        h2h_data: pd.DataFrame,
        home_team: str,
        away_team: str,
        league_name: str
    ) -> Dict[str, Any]:
        """Calculate predictions using simple statistical methods."""
        
        # Extract key statistics
        home_ppg = float(home_data.get('points_per_game', 0) or 0)
        away_ppg = float(away_data.get('points_per_game', 0) or 0)
        
        home_goals_for = float(home_data.get('goals_scored_per_match_all', 0) or 0)
        home_goals_against = float(home_data.get('goals_conceded_per_match_all', 0) or 0)
        away_goals_for = float(away_data.get('goals_scored_per_match_all', 0) or 0)
        away_goals_against = float(away_data.get('goals_conceded_per_match_all', 0) or 0)
        
        # Home advantage factor (typically 0.3-0.5 goals)
        home_advantage = 0.4
        
        # Calculate expected goals using simple method
        # Home team expected goals = (home attack + away defense weakness + home advantage) / 2
        home_attack_strength = home_goals_for
        away_defense_weakness = away_goals_against
        home_expected_goals = max(0, (home_attack_strength + away_defense_weakness + home_advantage) / 2)
        
        # Away team expected goals = (away attack + home defense weakness) / 2
        away_attack_strength = away_goals_for
        home_defense_weakness = home_goals_against
        away_expected_goals = max(0, (away_attack_strength + home_defense_weakness) / 2)
        
        # Calculate win probabilities using PPG and form
        home_strength = home_ppg + 0.3  # Home advantage
        away_strength = away_ppg
        total_strength = home_strength + away_strength
        
        if total_strength > 0:
            home_win_prob = home_strength / total_strength
            away_win_prob = away_strength / total_strength
        else:
            home_win_prob = 0.4
            away_win_prob = 0.3
        
        # Adjust for head-to-head if available
        if not h2h_data.empty:
            h2h_row = h2h_data.iloc[0]
            total_h2h_matches = h2h_row.get('total_matches', 0)
            
            if total_h2h_matches > 0:
                if h2h_row.get('home_team') == home_team:
                    home_h2h_wins = h2h_row.get('home_wins', 0)
                    away_h2h_wins = h2h_row.get('away_wins', 0)
                else:
                    home_h2h_wins = h2h_row.get('away_wins', 0)
                    away_h2h_wins = h2h_row.get('home_wins', 0)
                
                h2h_draws = h2h_row.get('draws', 0)
                
                # Weight H2H data (20% influence)
                h2h_weight = 0.2
                h2h_home_prob = home_h2h_wins / total_h2h_matches
                h2h_away_prob = away_h2h_wins / total_h2h_matches
                
                home_win_prob = (1 - h2h_weight) * home_win_prob + h2h_weight * h2h_home_prob
                away_win_prob = (1 - h2h_weight) * away_win_prob + h2h_weight * h2h_away_prob
        
        # Ensure probabilities are reasonable
        home_win_prob = max(0.1, min(0.8, home_win_prob))
        away_win_prob = max(0.1, min(0.8, away_win_prob))
        
        # Calculate draw probability
        draw_prob = max(0.1, 1.0 - home_win_prob - away_win_prob)
        
        # Normalize probabilities
        total_prob = home_win_prob + draw_prob + away_win_prob
        home_win_prob /= total_prob
        draw_prob /= total_prob
        away_win_prob /= total_prob
        
        # Determine prediction
        if home_win_prob > draw_prob and home_win_prob > away_win_prob:
            prediction = "Home"
        elif away_win_prob > draw_prob and away_win_prob > home_win_prob:
            prediction = "Away"
        else:
            prediction = "Draw"
        
        # Generate some likely scores based on expected goals
        correct_scores = self._generate_likely_scores(home_expected_goals, away_expected_goals)
        
        # Build prediction response
        predictions = {
            "main_predictions": {
                "three_way": {
                    "prediction": prediction,
                    "probabilities": {
                        "Home": round(home_win_prob, 3),
                        "Draw": round(draw_prob, 3),
                        "Away": round(away_win_prob, 3)
                    }
                },
                "over_under_2_5": {
                    "prediction": "Over" if (home_expected_goals + away_expected_goals) > 2.5 else "Under",
                    "probabilities": {
                        "Over": round(min(0.9, max(0.1, (home_expected_goals + away_expected_goals) / 4)), 3),
                        "Under": round(min(0.9, max(0.1, 1 - (home_expected_goals + away_expected_goals) / 4)), 3)
                    }
                }
            },
            "expected_goals": {
                "home": round(home_expected_goals, 2),
                "away": round(away_expected_goals, 2)
            },
            "correct_scores": correct_scores,
            "match_info": {
                "home_team": home_team,
                "away_team": away_team,
                "league": league_name,
                "prediction_method": "Simple Statistical Analysis"
            }
        }
        
        return predictions
    
    def _generate_likely_scores(self, home_xg: float, away_xg: float) -> Dict[str, float]:
        """Generate likely scores based on expected goals."""
        scores = {}
        
        # Common football scores with probabilities based on expected goals
        possible_scores = [
            (0, 0), (1, 0), (0, 1), (1, 1), (2, 0), (0, 2), (2, 1), (1, 2),
            (2, 2), (3, 0), (0, 3), (3, 1), (1, 3), (3, 2), (2, 3)
        ]
        
        for home_score, away_score in possible_scores:
            # Simple probability calculation based on how close the score is to expected goals
            home_diff = abs(home_score - home_xg)
            away_diff = abs(away_score - away_xg)
            
            # Higher probability for scores closer to expected goals
            prob = max(0.01, 0.3 * np.exp(-(home_diff + away_diff)))
            scores[f"{home_score}-{away_score}"] = round(prob, 3)
        
        # Normalize probabilities
        total_prob = sum(scores.values())
        if total_prob > 0:
            scores = {k: round(v / total_prob, 3) for k, v in scores.items()}
        
        # Return top 8 most likely scores
        sorted_scores = dict(sorted(scores.items(), key=lambda x: x[1], reverse=True)[:8])
        return sorted_scores
    
    def get_team_prediction_data(self, team_name: str, league_name: str) -> Dict[str, Any]:
        """Get prediction-relevant data for a team."""
        try:
            team_name = normalize_team_name(team_name, league_name)
            
            with get_database() as db:
                # Get team statistics
                team_stats = db.get_team_stats(team_name, league_name)
                if team_stats.empty:
                    return {}
                
                # Get recent form
                recent_form = db.get_team_form(team_name, league_name, 5)
                
                # Get league position
                league_table = db.get_league_table(league_name)
                team_position = None
                if not league_table.empty:
                    team_row = league_table[league_table['team_name'] == team_name]
                    if not team_row.empty:
                        team_position = team_row.iloc[0]['position']
                
                return {
                    "team_stats": team_stats.iloc[0].to_dict() if not team_stats.empty else {},
                    "recent_form": recent_form.to_dict('records') if not recent_form.empty else [],
                    "league_position": team_position
                }
                
        except Exception as e:
            logger.error(f"Error getting team data for {team_name}: {e}")
            return {}

# Global instance for web app usage
simple_prediction_service = SimplePredictionService()

def get_simple_prediction_service() -> SimplePredictionService:
    """Get the global simple prediction service instance."""
    return simple_prediction_service
