#!/usr/bin/env python3
"""
Database-based Prediction Service

This module provides a prediction service that uses the SQLite database
instead of CSV files while maintaining compatibility with the existing
prediction system. It serves as a bridge between the web application
and the comprehensive prediction models.
"""

import logging
import pandas as pd
import numpy as np
import os
import sys
from typing import Dict, Tuple, Optional, Any, List
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from database.football_db import get_database, normalize_team_name
from data_loading.database_loader import DatabaseDataLoader
from prediction.core import predict_match
from model_training.core import train_model
from feature_engineering.core import prepare_features
from feature_engineering.utils import get_column_mappings

logger = logging.getLogger(__name__)

class DatabasePredictionService:
    """
    Prediction service that uses the database for data loading and prediction.
    
    This service maintains compatibility with the existing CSV-based prediction
    system while using the database as the data source.
    """
    
    def __init__(self, db_path='data/football_betting.db'):
        """Initialize the prediction service with database connection."""
        self.db_path = db_path
        self.data_loader = DatabaseDataLoader(db_path)
        self._models_cache = {}
        self._league_data_cache = {}
        
    def get_available_leagues(self) -> List[str]:
        """Get list of leagues available for predictions."""
        return self.data_loader.get_available_leagues()
    
    def predict_match(
        self, 
        home_team: str, 
        away_team: str, 
        league_name: str,
        use_cached_models: bool = True
    ) -> Tuple[Dict[str, Any], Optional[str]]:
        """
        Predict the outcome of a match between two teams.
        
        Args:
            home_team: Name of the home team
            away_team: Name of the away team
            league_name: Name of the league
            use_cached_models: Whether to use cached models for faster predictions
            
        Returns:
            Tuple of (predictions_dict, error_message)
        """
        try:
            logger.info(f"Predicting {home_team} vs {away_team} in {league_name}")
            
            # Normalize team names
            home_team = normalize_team_name(home_team, league_name)
            away_team = normalize_team_name(away_team, league_name)
            
            # Load league data
            league_data = self._get_league_data(league_name)
            if not league_data:
                return {}, f"Could not load data for league: {league_name}"
            
            results, team_stats, league_stats, h2h_stats, league_table = league_data
            
            # Validate teams exist
            if not self._validate_teams(home_team, away_team, team_stats):
                return {}, f"One or both teams not found: {home_team}, {away_team}"
            
            # Get or train models
            models = self._get_models(league_name, league_data, use_cached_models)
            if not models:
                return {}, f"Could not load or train models for {league_name}"
            
            # Get column mappings
            mappings = get_column_mappings()
            combined_mapping = {**mappings["team_stats"], **mappings["h2h_stats"]}
            
            # Calculate average goals per match
            avg_goals_per_match = float(
                league_stats[league_stats["Stat"] == "avg_goals_per_match"]["Value"].values[0]
            )
            
            # Make prediction
            predictions, error_message, correct_scores = predict_match(
                models,
                home_team,
                away_team,
                team_stats,
                league_stats,
                h2h_stats,
                league_table,
                combined_mapping,
                models["three_way"]["feature_names"],
                avg_goals_per_match,
                label_encoders=models.get("label_encoders"),
                bias_correction=0.05,
                log_features=True
            )
            
            if error_message:
                return {}, error_message
            
            # Add metadata
            predictions["match_info"] = {
                "home_team": home_team,
                "away_team": away_team,
                "league": league_name
            }
            
            return predictions, None
            
        except Exception as e:
            logger.error(f"Error predicting match {home_team} vs {away_team}: {str(e)}")
            return {}, f"Prediction error: {str(e)}"
    
    def predict_multiple_matches(
        self, 
        matches: List[Tuple[str, str]], 
        league_name: str
    ) -> Dict[str, Tuple[Dict[str, Any], Optional[str]]]:
        """
        Predict multiple matches in a league.
        
        Args:
            matches: List of (home_team, away_team) tuples
            league_name: Name of the league
            
        Returns:
            Dictionary mapping match strings to (predictions, error_message)
        """
        results = {}
        
        # Load models once for all predictions
        league_data = self._get_league_data(league_name)
        if not league_data:
            error_msg = f"Could not load data for league: {league_name}"
            for home_team, away_team in matches:
                results[f"{home_team} vs {away_team}"] = ({}, error_msg)
            return results
        
        models = self._get_models(league_name, league_data, use_cached_models=True)
        if not models:
            error_msg = f"Could not load or train models for {league_name}"
            for home_team, away_team in matches:
                results[f"{home_team} vs {away_team}"] = ({}, error_msg)
            return results
        
        # Predict each match
        for home_team, away_team in matches:
            match_key = f"{home_team} vs {away_team}"
            predictions, error = self.predict_match(
                home_team, away_team, league_name, use_cached_models=True
            )
            results[match_key] = (predictions, error)
        
        return results
    
    def get_team_prediction_data(self, team_name: str, league_name: str) -> Dict[str, Any]:
        """
        Get prediction-relevant data for a team.
        
        Args:
            team_name: Name of the team
            league_name: Name of the league
            
        Returns:
            Dictionary containing team stats and form data
        """
        try:
            team_name = normalize_team_name(team_name, league_name)
            
            with get_database() as db:
                # Get team statistics
                team_stats = db.get_team_stats(team_name, league_name)
                if team_stats.empty:
                    return {}
                
                # Get recent form
                recent_form = db.get_team_form(team_name, league_name, 5)
                
                # Get league position
                league_table = db.get_league_table(league_name)
                team_position = None
                if not league_table.empty:
                    team_row = league_table[league_table['team_name'] == team_name]
                    if not team_row.empty:
                        team_position = team_row.iloc[0]['position']
                
                return {
                    "team_stats": team_stats.iloc[0].to_dict() if not team_stats.empty else {},
                    "recent_form": recent_form.to_dict('records') if not recent_form.empty else [],
                    "league_position": team_position
                }
                
        except Exception as e:
            logger.error(f"Error getting team data for {team_name}: {str(e)}")
            return {}
    
    def _get_league_data(self, league_name: str) -> Optional[Tuple]:
        """Get or cache league data."""
        if league_name not in self._league_data_cache:
            try:
                if not self.data_loader.validate_league_data(league_name):
                    logger.error(f"Invalid league data for {league_name}")
                    return None
                
                league_data = self.data_loader.load_league_data(league_name)
                self._league_data_cache[league_name] = league_data
            except Exception as e:
                logger.error(f"Error loading league data for {league_name}: {str(e)}")
                return None
        
        return self._league_data_cache[league_name]
    
    def _validate_teams(self, home_team: str, away_team: str, team_stats: pd.DataFrame) -> bool:
        """Validate that both teams exist in the team stats."""
        teams = team_stats['Team'].values
        return home_team in teams and away_team in teams
    
    def _get_models(
        self, 
        league_name: str, 
        league_data: Tuple, 
        use_cached_models: bool = True
    ) -> Optional[Dict[str, Any]]:
        """Get or train models for a league."""
        if use_cached_models and league_name in self._models_cache:
            return self._models_cache[league_name]
        
        try:
            logger.info(f"Training models for {league_name}")
            
            results, team_stats, league_stats, h2h_stats, league_table = league_data
            
            # Prepare features
            mappings = get_column_mappings()
            combined_mapping = {**mappings["team_stats"], **mappings["h2h_stats"]}
            
            # Use empty team name mapping for database data (already normalized)
            team_name_mapping = {}
            
            prepared_data = prepare_features(
                results, team_stats, league_stats, h2h_stats, league_table,
                combined_mapping, team_name_mapping
            )
            
            if not prepared_data:
                logger.error(f"Failed to prepare features for {league_name}")
                return None
            
            X, y_dict, label_encoders, feature_names = prepared_data
            
            # Train models
            models = train_model(X, y_dict, label_encoders)
            if not models:
                logger.error(f"Failed to train models for {league_name}")
                return None
            
            # Add label encoders to models
            models["label_encoders"] = label_encoders
            
            # Cache models
            if use_cached_models:
                self._models_cache[league_name] = models
            
            logger.info(f"Successfully trained models for {league_name}")
            return models
            
        except Exception as e:
            logger.error(f"Error training models for {league_name}: {str(e)}")
            return None
    
    def clear_cache(self):
        """Clear all cached data and models."""
        self._models_cache.clear()
        self._league_data_cache.clear()
        logger.info("Cleared prediction service cache")

# Global instance for web app usage
prediction_service = DatabasePredictionService()

def get_prediction_service() -> DatabasePredictionService:
    """Get the global prediction service instance."""
    return prediction_service
