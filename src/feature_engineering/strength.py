"""
Team strength calculation functionality.
Handles attack, defense, and overall team strength calculations.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Optional
from .constants import (
    HOME_BOOST,
    AWAY_REDUCTION,
    SCORING_WEIGHT,
    DEFENSIVE_WEIGHT,
    WIN_RATE_WEIGHT,
    FORM_WEIGHT,
    HOME_VENUE_BOOST,
    AWAY_VENUE_REDUCTION,
    BASE_WEIGHT,
    FORM_ADJUSTMENT_WEIGHT
)

logger = logging.getLogger(__name__)

def calculate_team_strength(
    team_stats: pd.Series,
    league_stats_dict: Dict[str, float],
    is_home: bool = True
) -> float:
    """
    Calculate overall team strength based on various metrics.

    Args:
        team_stats: Statistics for the team
        league_stats_dict: Dictionary of league-wide statistics
        is_home: Whether the team is playing at home

    Returns:
        Calculated team strength (0-1 scale)
    """
    try:
        # Get relevant statistics
        if is_home:
            goals_scored = team_stats["goals_scored_per_match_home"]
            goals_conceded = team_stats["goals_conceded_per_match_home"]
            wins = team_stats["total_home_wins"]
            matches = team_stats["total_home_played"]
        else:
            goals_scored = team_stats["goals_scored_per_match_away"]
            goals_conceded = team_stats["goals_conceded_per_match_away"]
            wins = team_stats["total_away_wins"]
            matches = team_stats["total_away_played"]

        # Calculate various strength components using league averages
        league_avg_goals = float(league_stats_dict.get("avg_goals_per_match", 2.5))
        scoring_strength = goals_scored / league_avg_goals
        defensive_strength = 1 - (goals_conceded / league_avg_goals)
        win_rate = wins / matches if matches > 0 else 0.0
        form = team_stats["ppg_last_8"] / 3.0  # Normalize by max points possible

        # Combine components with weights
        strength = (
            SCORING_WEIGHT * scoring_strength
            + DEFENSIVE_WEIGHT * defensive_strength
            + WIN_RATE_WEIGHT * win_rate
            + FORM_WEIGHT * form
        )

        # Apply venue adjustment
        strength *= HOME_BOOST if is_home else AWAY_REDUCTION

        # Ensure result is between 0 and 1
        return np.clip(strength, 0, 1)

    except Exception as e:
        logger.error(f"Error calculating team strength: {str(e)}")
        return 0.5  # Return neutral strength on error

def calculate_attack_strength(
    team_stats: pd.Series,
    league_stats_dict: Dict[str, float],
    is_home: bool = True
) -> float:
    """
    Calculate attack strength with enhanced consideration of form and opposition quality.

    Args:
        team_stats: Statistics for the team
        league_stats_dict: Dictionary of league-wide statistics
        is_home: Whether the team is playing at home

    Returns:
        Calculated attack strength
    """
    try:
        # Get base scoring rate with NaN handling
        if is_home:
            goals_scored = team_stats.get("goals_scored_per_match_home", 1.0)
            ppg_last_8 = team_stats.get("ppg_last_8", np.nan)
        else:
            goals_scored = team_stats.get("goals_scored_per_match_away", 1.0)
            ppg_last_8 = team_stats.get("ppg_last_8", np.nan)

        # Handle NaN in form factor
        if pd.isna(ppg_last_8):
            # Calculate from basic stats if available
            total_wins = team_stats.get("total_wins", 0)
            total_draws = team_stats.get("total_draws", 0)
            total_played = team_stats.get("total_played", 1)
            ppg_last_8 = (total_wins * 3 + total_draws * 1) / max(total_played, 1)

        form_factor = ppg_last_8 / 2.0

        # Get league averages
        league_avg = float(league_stats_dict.get("avg_goals_per_match", 2.5))
        league_home_avg = float(league_stats_dict.get("home_goals_per_match", 1.5))
        league_away_avg = float(league_stats_dict.get("away_goals_per_match", 1.0))

        # Calculate base attack strength
        if is_home:
            base_strength = goals_scored / league_home_avg if league_home_avg > 0 else 1.0
        else:
            base_strength = goals_scored / league_away_avg if league_away_avg > 0 else 1.0

        # Apply form adjustment with exponential weighting
        form_weight = np.exp(form_factor - 1)  # Exponential scaling of form
        form_adjusted = base_strength * (BASE_WEIGHT + FORM_ADJUSTMENT_WEIGHT * form_weight)

        # Apply venue adjustment with dynamic factor
        venue_factor = HOME_VENUE_BOOST if is_home else AWAY_VENUE_REDUCTION
        final_strength = form_adjusted * venue_factor

        # Normalize to reasonable range with smoother bounds
        final_strength = 0.5 + 1.5 / (1 + np.exp(-2 * (final_strength - 1)))

        return final_strength

    except Exception as e:
        logger.error(f"Error calculating attack strength: {str(e)}")
        return 1.0  # Return neutral strength on error

def calculate_defense_strength(
    team_stats: pd.Series,
    league_stats_dict: Dict[str, float],
    is_home: bool = True
) -> float:
    """
    Calculate defense strength with enhanced consideration of clean sheets and goals conceded.

    Args:
        team_stats: Statistics for the team
        league_stats_dict: Dictionary of league-wide statistics
        is_home: Whether the team is playing at home

    Returns:
        Calculated defense strength
    """
    try:
        # Get defense stats with NaN handling
        if is_home:
            goals_conceded = team_stats.get("goals_conceded_per_match_home", 1.0)
            clean_sheets = team_stats.get("clean_sheets_home", 0)
            matches_played = team_stats.get("total_home_played", 1)
        else:
            goals_conceded = team_stats.get("goals_conceded_per_match_away", 1.0)
            clean_sheets = team_stats.get("clean_sheets_away", 0)
            matches_played = team_stats.get("total_away_played", 1)

        # Handle NaN values
        if pd.isna(goals_conceded):
            goals_conceded = 1.0
        if pd.isna(matches_played) or matches_played == 0:
            matches_played = 1

        league_avg = float(league_stats_dict.get("avg_goals_per_match", 2.5))

        # Calculate clean sheet ratio
        clean_sheet_ratio = clean_sheets / matches_played if matches_played > 0 else 0

        # Calculate base defense strength
        base_strength = goals_conceded / league_avg if league_avg > 0 else 1.0

        # Invert and adjust with clean sheet bonus
        defense_strength = (2 - base_strength) * (1 + 0.2 * clean_sheet_ratio)

        # Apply venue adjustment with dynamic factor
        venue_factor = HOME_VENUE_BOOST if is_home else AWAY_VENUE_REDUCTION
        defense_strength *= venue_factor

        # Normalize to reasonable range with smoother bounds
        defense_strength = 0.5 + 1.5 / (1 + np.exp(-2 * (defense_strength - 1)))

        return defense_strength

    except Exception as e:
        logger.error(f"Error calculating defense strength: {str(e)}")
        return 1.0  # Return neutral strength on error

def calculate_form(
    team_stats: pd.Series,
    recent_weight: float = 0.7
) -> float:
    """
    Calculate team form with exponential decay for recent results.

    Args:
        team_stats: Statistics for the team
        recent_weight: Weight given to recent performance

    Returns:
        Calculated form value (0-1 scale)
    """
    try:
        # Handle NaN values with safe defaults
        ppg_last_8 = team_stats.get("ppg_last_8", np.nan)
        overall_ppg = team_stats.get("points_per_game", np.nan)

        # If both are NaN, calculate from basic stats
        if pd.isna(ppg_last_8) and pd.isna(overall_ppg):
            # Calculate basic PPG from wins/draws/losses
            total_wins = team_stats.get("total_wins", 0)
            total_draws = team_stats.get("total_draws", 0)
            total_played = team_stats.get("total_played", 1)
            overall_ppg = (total_wins * 3 + total_draws * 1) / max(total_played, 1)
            ppg_last_8 = overall_ppg  # Use overall as approximation
        elif pd.isna(ppg_last_8):
            ppg_last_8 = overall_ppg
        elif pd.isna(overall_ppg):
            overall_ppg = ppg_last_8

        # Calculate recent goals scored and conceded with safe defaults
        recent_goals_scored = team_stats.get("avg_goals_scored_last_8", np.nan)
        recent_goals_conceded = team_stats.get("avg_goals_conceded_last_8", np.nan)

        # Use overall stats as fallback
        if pd.isna(recent_goals_scored):
            recent_goals_scored = team_stats.get("goals_scored_per_match_all", 1.0)
        if pd.isna(recent_goals_conceded):
            recent_goals_conceded = team_stats.get("goals_conceded_per_match_all", 1.0)

        # Calculate goal difference impact
        goal_diff_factor = np.tanh((recent_goals_scored - recent_goals_conceded) / 2)

        # Weight recent form more heavily with goal difference consideration
        form = (recent_weight * ppg_last_8 + (1 - recent_weight) * overall_ppg) * (
            1 + 0.2 * goal_diff_factor
        )

        # Normalize to [0, 1] range
        form = np.clip(form / 3.0, 0, 1)

        return form

    except Exception as e:
        logger.error(f"Error calculating form: {str(e)}")
        return 0.5  # Return neutral form on error
