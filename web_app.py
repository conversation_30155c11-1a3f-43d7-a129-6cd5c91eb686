#!/usr/bin/env python3
"""
Football Database Web Interface


A Flask web application providing a clean GUI to explore the football database.
Features league browsing, team analysis, head-to-head comparisons, and more.

"""
from flask import Flask, render_template, request, jsonify, redirect, url_for
import sys
import os
import pandas as pd
import json
from datetime import datetime

# Add src to path for database imports
sys.path.append('src')
from database.football_db import FootballDatabase, get_database, QueryBuilder, normalize_team_name

app = Flask(__name__)

@app.template_filter('format_date')
def format_date(date_str):
    if not date_str:
        return ""
    try:
        # Assuming the date is in a format like 'DD Mon'
        dt = datetime.strptime(date_str, '%d %b')
        now = datetime.now()
        if dt.month > now.month:
            dt = dt.replace(year=now.year - 1)
        else:
            dt = dt.replace(year=now.year)
        return dt.strftime('%d %b %Y')
    except (ValueError, TypeError):
        return date_str

app.secret_key = 'football_betting_secret_key_2024'

# Global database instance
db_path = 'data/football_betting.db'

@app.context_processor
def inject_leagues():
    """Inject leagues data into all templates for sidebar navigation"""
    try:
        with get_database() as db:
            leagues_df = db.get_leagues()
            # Sort leagues alphabetically and group by country/region
            leagues_list = sorted(leagues_df['league_name'].tolist())
            
            # Group leagues by country prefix for better organization
            grouped_leagues = {}
            for league in leagues_list:
                country = league.split('_')[0]
                if country not in grouped_leagues:
                    grouped_leagues[country] = []
                grouped_leagues[country].append(league)
            
            return dict(sidebar_leagues=grouped_leagues, all_leagues=leagues_list)
    except Exception as e:
        print(f"Error getting leagues for sidebar: {e}")
        return dict(sidebar_leagues={}, all_leagues=[])

def get_db_summary():
    try:
        with get_database() as db:
            return db.get_database_summary()
    except Exception as e:
        print(f"Error getting database summary: {e}")
        return {}

def safe_float(value, default=0.0):
    try:
        if pd.isna(value) or value is None:
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value, default=0):
    try:
        if pd.isna(value) or value is None:
            return default
        return int(value)
    except (ValueError, TypeError):
        return default

@app.route('/')
def index():
    summary = get_db_summary()
    
    # Get sample leagues for quick access
    try:
        with get_database() as db:
            leagues = db.get_leagues()
            sample_leagues = leagues.head(10)['league_name'].tolist()
    except Exception as e:
        print(f"Error getting leagues: {e}")
        sample_leagues = []
    
    return render_template('index.html', 
                         summary=summary, 
                         sample_leagues=sample_leagues)

@app.route('/leagues')
def leagues():
    try:
        with get_database() as db:
            leagues_df = db.get_leagues()
            leagues_list = leagues_df.to_dict('records')
    except Exception as e:
        print(f"Error getting leagues: {e}")
        leagues_list = []
    
    return render_template('leagues.html', leagues=leagues_list)

@app.route('/league/<league_name>')
def league_detail(league_name):
    try:
        with get_database() as db:
            # Get league table
            table = db.get_league_table(league_name)
            table_data = []
            if not table.empty:
                for _, row in table.iterrows():
                    table_data.append({
                        'position': safe_int(row['position']),
                        'team_name': row['team_name'],
                        'MP': safe_int(row['MP']),
                        'W': safe_int(row['W']),
                        'D': safe_int(row['D']),
                        'L': safe_int(row['L']),
                        'GF': safe_int(row['GF']),
                        'GA': safe_int(row['GA']),
                        'GD': safe_int(row['GD']),
                        'Pts': safe_int(row['Pts'])
                    })
            
            # Get league statistics
            league_stats = db.get_league_stats(league_name)
            stats_data = {}
            if not league_stats.empty:
                for _, row in league_stats.iterrows():
                    stats_data[row['stat_name']] = safe_float(row['stat_value'])
            
            # Get top scorers
            top_scorers = db.get_league_top_scorers(league_name, 10)
            scorers_data = []
            if not top_scorers.empty:
                for _, row in top_scorers.iterrows():
                    scorers_data.append({
                        'team_name': row['team_name'],
                        'goals_scored_all': safe_int(row['goals_scored_all']),
                        'goals_scored_per_match_all': safe_float(row['goals_scored_per_match_all']),
                        'total_played': safe_int(row['total_played'])
                    })
            
            # Get recent matches
            recent_matches = db.get_match_results(league_name, limit=10)
            matches_data = []
            if not recent_matches.empty:
                for _, row in recent_matches.iterrows():
                    matches_data.append({
                        'match_date': row['match_date'],
                        'home_team': row['home_team'],
                        'away_team': row['away_team'],
                        'home_score': safe_int(row['home_score']) if pd.notna(row['home_score']) else None,
                        'away_score': safe_int(row['away_score']) if pd.notna(row['away_score']) else None,
                        'result_code': safe_int(row['result_code']) if pd.notna(row['result_code']) else None
                    })
            
            # Get teams for dropdown
            teams = db.get_teams(league_name)
            teams_list = teams['team_name'].tolist() if not teams.empty else []
            
    except Exception as e:
        print(f"Error getting league details for {league_name}: {e}")
        table_data = []
        stats_data = {}
        scorers_data = []
        matches_data = []
        teams_list = []
    
    return render_template('league_detail.html',
                         league_name=league_name,
                         table=table_data,
                         stats=stats_data,
                         top_scorers=scorers_data,
                         recent_matches=matches_data,
                         teams=teams_list)

@app.route('/team/<league_name>/<team_name>')
def team_detail(league_name, team_name):
    try:
        with get_database() as db:
            # Normalize team name for all database lookups
            normalized_team_name = normalize_team_name(team_name, league_name)
            
            # Get team statistics
            team_stats = db.get_team_stats(normalized_team_name, league_name)

            stats_data = {}
            if not team_stats.empty:
                stats = team_stats.iloc[0]
                stats_data = {
                    'total_played': safe_int(stats['total_played']),
                    'total_wins': safe_int(stats['total_wins']),
                    'total_draws': safe_int(stats['total_draws']),
                    'total_losses': safe_int(stats['total_losses']),
                    'points_per_game': safe_float(stats['points_per_game']),
                    'home_points_per_game': safe_float(stats['home_points_per_game']),
                    'away_points_per_game': safe_float(stats['away_points_per_game']),
                    'goals_scored_all': safe_int(stats['goals_scored_all']),
                    'goals_conceded_all': safe_int(stats['goals_conceded_all']),
                    'goals_scored_per_match_all': safe_float(stats['goals_scored_per_match_all']),
                    'goals_conceded_per_match_all': safe_float(stats['goals_conceded_per_match_all']),
                    'goals_scored_home': safe_int(stats['goals_scored_home']),
                    'goals_scored_away': safe_int(stats['goals_scored_away']),
                    'goals_conceded_home': safe_int(stats['goals_conceded_home']),
                    'goals_conceded_away': safe_int(stats['goals_conceded_away']),
                    'ppg_last_8': safe_float(stats['ppg_last_8']),
                    'avg_goals_scored_last_8': safe_float(stats['avg_goals_scored_last_8']),
                    'avg_goals_conceded_last_8': safe_float(stats['avg_goals_conceded_last_8'])
                }

            # Get recent form
            recent_form = db.get_team_form(normalized_team_name, league_name, 10)
            form_data = []
            if not recent_form.empty:
                for _, row in recent_form.iterrows():
                    form_data.append({
                        'match_date': row['match_date'],
                        'home_team': row['home_team'],
                        'away_team': row['away_team'],
                        'home_score': safe_int(row['home_score']) if pd.notna(row['home_score']) else None,
                        'away_score': safe_int(row['away_score']) if pd.notna(row['away_score']) else None,
                        'venue': row['venue'],
                        'result': row['result']
                    })

            # Get all teams for sidebar and H2H comparison
            teams = db.get_teams(league_name)
            teams_list = sorted(teams['team_name'].tolist()) if not teams.empty else []
            other_teams = [t for t in teams_list if t != team_name]

    except Exception as e:
        print(f"Error getting team details for {team_name}: {e}")
        stats_data = {}
        form_data = []
        teams_list = []
        other_teams = []

    return render_template('team_detail.html',
                         league_name=league_name,
                         team_name=team_name,
                         teams=teams_list,
                         stats=stats_data,
                         recent_form=form_data,
                         other_teams=other_teams)

@app.route('/h2h/<league_name>/<team1>/<team2>')
def head_to_head(league_name, team1, team2):
    try:
        with get_database() as db:
            # Normalize team names
            normalized_team1 = normalize_team_name(team1, league_name)
            normalized_team2 = normalize_team_name(team2, league_name)
            
            # Get H2H statistics
            h2h_stats = db.get_head_to_head_stats(normalized_team1, normalized_team2, league_name)
            h2h_data = {}
            if not h2h_stats.empty:
                h2h = h2h_stats.iloc[0]
                h2h_data = {
                    'matchup': h2h['matchup'],
                    'total_matches': safe_int(h2h['total_matches']),
                    'home_team': h2h['home_team'],
                    'away_team': h2h['away_team'],
                    'home_win_percentage': safe_float(h2h['home_win_percentage']),
                    'away_win_percentage': safe_float(h2h['away_win_percentage']),
                    'draw_percentage': safe_float(h2h['draw_percentage']),
                    'home_wins': safe_int(h2h['home_wins']),
                    'away_wins': safe_int(h2h['away_wins']),
                    'draws': safe_int(h2h['draws']),
                    'home_goals': safe_int(h2h['home_goals']),
                    'away_goals': safe_int(h2h['away_goals']),
                    'btts_percentage': safe_float(h2h['btts_percentage']),
                    'over_1_5_percentage': safe_float(h2h['over_1_5_percentage']),
                    'over_2_5_percentage': safe_float(h2h['over_2_5_percentage']),
                    'over_3_5_percentage': safe_float(h2h['over_3_5_percentage']),
                    'home_clean_sheet_percentage': safe_float(h2h['home_clean_sheet_percentage']),
                    'away_clean_sheet_percentage': safe_float(h2h['away_clean_sheet_percentage']),
                    'recent_results': h2h['recent_results']
                }
            
            # Get individual team stats
            team1_stats = db.get_team_stats(normalized_team1, league_name)
            team2_stats = db.get_team_stats(normalized_team2, league_name)
            
            team1_data = {}
            team2_data = {}
            
            if not team1_stats.empty:
                stats = team1_stats.iloc[0]
                team1_data = {
                    'goals_scored_per_match_all': safe_float(stats['goals_scored_per_match_all']),
                    'goals_conceded_per_match_all': safe_float(stats['goals_conceded_per_match_all']),
                    'points_per_game': safe_float(stats['points_per_game']),
                    'total_played': safe_int(stats['total_played'])
                }
            
            if not team2_stats.empty:
                stats = team2_stats.iloc[0]
                team2_data = {
                    'goals_scored_per_match_all': safe_float(stats['goals_scored_per_match_all']),
                    'goals_conceded_per_match_all': safe_float(stats['goals_conceded_per_match_all']),
                    'points_per_game': safe_float(stats['points_per_game']),
                    'total_played': safe_int(stats['total_played'])
                }
            
            # Get all teams for sidebar navigation
            teams = db.get_teams(league_name)
            teams_list = sorted(teams['team_name'].tolist()) if not teams.empty else []
            
    except Exception as e:
        print(f"Error getting H2H data for {team1} vs {team2}: {e}")
        h2h_data = {}
        team1_data = {}
        team2_data = {}
        teams_list = []
    
    return render_template('head_to_head.html',
                         league_name=league_name,
                         team1=team1,
                         team2=team2,
                         teams=teams_list,
                         h2h=h2h_data,
                         team1_stats=team1_data,
                         team2_stats=team2_data)

@app.route('/search')
def search():
    query = request.args.get('q', '').strip()
    results = {'leagues': [], 'teams': []}
    
    if query and len(query) >= 2:
        try:
            with get_database() as db:
                # Search leagues
                leagues_df = db.execute_query(
                    "SELECT league_name FROM leagues WHERE league_name LIKE ? LIMIT 10",
                    (f'%{query}%',)
                )
                results['leagues'] = leagues_df['league_name'].tolist()
                
                # Search teams
                teams_df = db.execute_query(
                    "SELECT t.team_name, l.league_name "
                    "FROM teams t "
                    "JOIN leagues l ON t.league_id = l.league_id "
                    "WHERE t.team_name LIKE ? "
                    "LIMIT 10",
                    (f'%{query}%',)
                )
                
                results['teams'] = teams_df.to_dict('records')
                
        except Exception as e:
            print(f"Error searching for '{query}': {e}")
    
    return render_template('search.html', query=query, results=results)

@app.route('/api/leagues')
def api_leagues():
    try:
        with get_database() as db:
            leagues = db.get_leagues()
            return jsonify(leagues['league_name'].tolist())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/teams/<league_name>')
def api_teams(league_name):
    try:
        with get_database() as db:
            teams = db.get_teams(league_name)
            return jsonify(teams['team_name'].tolist())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats/<league_name>/<team_name>')
def api_team_stats(league_name, team_name):
    try:
        with get_database() as db:
            stats = db.get_team_stats(team_name, league_name)
            if stats.empty:
                return jsonify({'error': 'Team not found'}), 404
            
            # Convert to dict and handle NaN values
            stats_dict = stats.iloc[0].to_dict()
            for key, value in stats_dict.items():
                if pd.isna(value):
                    stats_dict[key] = None
            
            return jsonify(stats_dict)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Check if database exists
    if not os.path.exists(db_path):
        print(f"Database not found: {db_path}")
        print("Please run: python create_database.py")
        exit(1)
    
    print("Starting Football Database Web Interface...")
    print(f"Database: {db_path}")
    print("Access the web interface at: http://localhost:5010")
    
    app.run(debug=True, host='0.0.0.0', port=5010)