#!/usr/bin/env python3
"""
Fix Bolivia CDT Real Oruro Team Mapping

This script investigates and fixes the CDT Real Oruro team mapping issue
in the Bolivia Liga de Futbol Profesional Boliviano.
"""

import sys
sys.path.append('src')

from database.football_db import get_database

def analyze_bolivia_oruro_issue():
    """Analyze the CDT Real Oruro mapping issue."""
    print("🔍 BOLIVIA CDT REAL ORURO MAPPING ANALYSIS")
    print("=" * 50)
    
    with get_database() as db:
        # Check all teams in Bolivia league
        all_teams = db.execute_query('''
            SELECT DISTINCT t.team_name, t.team_id, t.canonical_team_id,
                   COUNT(ts.team_id) as stat_count
            FROM teams t
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = 'BOLIVIA_LIGA_DE_FUTBOL_PROFESIONAL_BOLIVIANO'
            GROUP BY t.team_name, t.team_id, t.canonical_team_id
            ORDER BY stat_count DESC, t.team_name
        ''')
        
        print("📊 All teams in Bolivia league:")
        print("-" * 40)
        
        teams_with_stats = []
        teams_without_stats = []
        
        for _, row in all_teams.iterrows():
            team_name = row['team_name']
            stat_count = row['stat_count']
            
            if stat_count > 0:
                teams_with_stats.append(team_name)
                print(f"✅ {team_name} ({stat_count} stats)")
            else:
                teams_without_stats.append(team_name)
                print(f"❌ {team_name} (no stats)")
        
        print(f"\n📈 SUMMARY:")
        print(f"   Teams with stats: {len(teams_with_stats)}")
        print(f"   Teams without stats: {len(teams_without_stats)}")
        
        # Check if there are any potential matches
        print(f"\n🔍 TEAMS WITHOUT STATS:")
        for team in teams_without_stats:
            print(f"   • {team}")
            
        # Check CSV teams vs database teams
        print(f"\n📄 CSV ANALYSIS:")
        csv_teams = [
            "Always Ready", "Aurora", "Blooming", "Bolivar", "Guabira", 
            "Gualberto V. Sj", "I. Petrolero", "Nacional Potosi", "Oriente P.",
            "Real Tomayapo", "Royal Pari", "Sa Bulo Bulo", "Santa Cruz", 
            "The Strongest", "U. De Vinto", "Wilstermann"
        ]
        
        print(f"   CSV teams: {len(csv_teams)}")
        print(f"   Database teams with stats: {len(teams_with_stats)}")
        
        # Find teams in database but not in CSV
        db_teams_with_stats_clean = [team.replace('Club ', '').replace('CD ', '').replace('CDT ', '') for team in teams_with_stats]
        
        missing_from_csv = []
        for team in teams_without_stats:
            clean_team = team.replace('Club ', '').replace('CD ', '').replace('CDT ', '')
            if clean_team not in csv_teams and clean_team not in db_teams_with_stats_clean:
                missing_from_csv.append(team)
        
        print(f"\n❌ TEAMS IN DATABASE BUT NOT IN CSV:")
        for team in missing_from_csv:
            print(f"   • {team}")
            
        return teams_with_stats, teams_without_stats

def attempt_oruro_fix():
    """Attempt to fix CDT Real Oruro by finding a suitable mapping."""
    print(f"\n🔧 ATTEMPTING CDT REAL ORURO FIX")
    print("=" * 40)
    
    with get_database() as db:
        # Check if there's a team that might be Oruro with different name
        # Look for teams with "Real" in the name that have stats
        potential_matches = db.execute_query('''
            SELECT DISTINCT t.team_name, COUNT(ts.team_id) as stat_count
            FROM teams t
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = 'BOLIVIA_LIGA_DE_FUTBOL_PROFESIONAL_BOLIVIANO'
            AND (t.team_name LIKE '%Real%' OR t.team_name LIKE '%Tomayapo%')
            GROUP BY t.team_name
            ORDER BY stat_count DESC
        ''')
        
        print("🔍 Potential matches for CDT Real Oruro:")
        for _, row in potential_matches.iterrows():
            team_name = row['team_name']
            stat_count = row['stat_count']
            print(f"   • {team_name}: {stat_count} stats")
            
        # Check if Real Tomayapo might actually be Real Oruro
        real_tomayapo_stats = db.get_team_stats('Real Tomayapo', 'BOLIVIA_LIGA_DE_FUTBOL_PROFESIONAL_BOLIVIANO')
        
        if not real_tomayapo_stats.empty:
            print(f"\n📊 Real Tomayapo stats (potential Oruro):")
            stats = real_tomayapo_stats.iloc[0]
            print(f"   PPG: {stats.get('points_per_game', 'N/A')}")
            print(f"   Goals For/Game: {stats.get('goals_for_per_game', 'N/A')}")
            print(f"   Goals Against/Game: {stats.get('goals_against_per_game', 'N/A')}")
            
            # This might be the team we need to map CDT Real Oruro to
            print(f"\n💡 POTENTIAL SOLUTION:")
            print(f"   Map 'CDT Real Oruro' → 'Real Tomayapo'")
            print(f"   This would give CDT Real Oruro access to stats")
            
            return True, 'Real Tomayapo'
        
        return False, None

def apply_oruro_fix():
    """Apply the CDT Real Oruro fix."""
    success, target_team = attempt_oruro_fix()
    
    if not success:
        print("❌ No suitable mapping found for CDT Real Oruro")
        return False
        
    print(f"\n🔧 APPLYING FIX: CDT Real Oruro → {target_team}")
    print("-" * 50)
    
    with get_database() as db:
        # Get team IDs
        oruro_id = db.execute_scalar('''
            SELECT t.team_id FROM teams t
            JOIN leagues l ON t.league_id = l.league_id
            WHERE t.team_name = 'CDT Real Oruro' 
            AND l.league_name = 'BOLIVIA_LIGA_DE_FUTBOL_PROFESIONAL_BOLIVIANO'
        ''')
        
        target_id = db.execute_scalar('''
            SELECT t.team_id FROM teams t
            JOIN leagues l ON t.league_id = l.league_id
            WHERE t.team_name = ? 
            AND l.league_name = 'BOLIVIA_LIGA_DE_FUTBOL_PROFESIONAL_BOLIVIANO'
        ''', (target_team,))
        
        if not oruro_id or not target_id:
            print("❌ Could not find team IDs")
            return False
            
        print(f"   CDT Real Oruro ID: {oruro_id}")
        print(f"   {target_team} ID: {target_id}")
        
        # Update canonical mapping
        try:
            db.conn.execute(
                'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                (target_id, oruro_id)
            )
            db.conn.commit()
            
            print(f"✅ Successfully mapped CDT Real Oruro to {target_team}")
            
            # Test the fix
            print(f"\n🧪 TESTING THE FIX:")
            oruro_stats = db.get_team_stats('CDT Real Oruro', 'BOLIVIA_LIGA_DE_FUTBOL_PROFESIONAL_BOLIVIANO')
            
            if not oruro_stats.empty:
                stats = oruro_stats.iloc[0]
                ppg = stats.get('points_per_game', 'N/A')
                print(f"✅ CDT Real Oruro now has stats! PPG: {ppg}")
                return True
            else:
                print("❌ Fix didn't work - still no stats")
                return False
                
        except Exception as e:
            print(f"❌ Error applying fix: {e}")
            return False

def main():
    """Main function."""
    print("🇧🇴 BOLIVIA LEAGUE CDT REAL ORURO FIX")
    print("=" * 50)
    
    # Analyze the issue
    teams_with_stats, teams_without_stats = analyze_bolivia_oruro_issue()
    
    # Attempt to fix
    if 'CDT Real Oruro' in teams_without_stats:
        success = apply_oruro_fix()
        
        if success:
            print(f"\n🎉 SUCCESS! CDT Real Oruro mapping fixed!")
            print(f"You can now run predictions with CDT Real Oruro vs Wilstermann")
        else:
            print(f"\n❌ Fix failed. CDT Real Oruro still has no stats.")
            print(f"This appears to be a data collection issue.")
            print(f"CDT Real Oruro exists in the league table but not in team stats CSV.")
    else:
        print(f"\n✅ CDT Real Oruro already has stats!")

if __name__ == "__main__":
    main()
