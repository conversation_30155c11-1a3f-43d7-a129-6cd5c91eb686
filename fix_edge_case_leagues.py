#!/usr/bin/env python3
"""
Fix Edge Case Leagues

This script fixes the specific issues found in the 40 edge case leagues:
1. Circular references (teams with stats pointing elsewhere)
2. Complex mappings (teams pointing to teams without stats)
3. Name mismatches between database and CSV
"""

import sys
import os
from typing import Dict, List, Tuple

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

# Edge case leagues from investigation
EDGE_CASE_LEAGUES = [
    "ARMENIA_PREMIER_LEAGUE", "AUSTRALIA_A_LEAGUE", "AUSTRIA_BUNDESLIGA",
    "BELGIUM_FIRST_DIVISION_A", "BRAZIL_SERIE_A", "BULGARIA_FIRST_LEAGUE",
    "CANADA_PREMIER_LEAGUE", "CHILE_PRIMERA_DIVISION", "COLOMBIA_PRIMERA_A",
    "COSTA_RICA_PRIMERA_DIVISION", "CROATIA_PRVA_HNL", "CZECH_REPUBLIC_FIRST_LEAGUE",
    "DENMARK_SUPERLIGA", "ECUADOR_SERIE_A", "ENGLAND_CHAMPIONSHIP",
    "ENGLAND_LEAGUE_1", "ENGLAND_PREMIER_LEAGUE", "ESTONIA_MEISTRILIIGA",
    "FINLAND_VEIKKAUSLIIGA", "FRANCE_LIGUE_1", "FRANCE_LIGUE_2",
    "GEORGIA_EROVNULI_LIGA", "GERMANY_BUNDESLIGA", "GREECE_SUPER_LEAGUE",
    "HUNGARY_NB_I", "ICELAND_URVALSDEILD", "IRELAND_PREMIER_DIVISION",
    "ISRAEL_PREMIER_LEAGUE", "ITALY_SERIE_A", "JAPAN_J1_LEAGUE",
    "LATVIA_VIRSLIGA", "LUXEMBOURG_NATIONAL_DIVISION", "MALTA_PREMIER_LEAGUE",
    "MEXICO_LIGA_MX", "NETHERLANDS_EREDIVISIE", "NORWAY_ELITESERIEN",
    "POLAND_EKSTRAKLASA", "PORTUGAL_LIGA_NOS", "SPAIN_LA_LIGA", "SWEDEN_ALLSVENSKAN"
]

def fix_circular_references(league_name: str) -> int:
    """Fix teams with stats that point to other teams instead of themselves."""
    fixes_applied = 0
    
    with get_database() as db:
        # Find teams with stats that don't point to themselves
        problematic_teams = db.execute_query('''
            SELECT t.team_id, t.team_name, t.canonical_team_id, 
                   COUNT(ts.team_id) as stat_count
            FROM teams t
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ? AND t.team_id != t.canonical_team_id
            GROUP BY t.team_id, t.team_name, t.canonical_team_id
            HAVING stat_count > 0
        ''', (league_name,))
        
        for _, team in problematic_teams.iterrows():
            team_id = team['team_id']
            team_name = team['team_name']
            stat_count = team['stat_count']
            
            # Make the team canonical (point to itself)
            db.conn.execute(
                'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                (team_id, team_id)
            )
            fixes_applied += 1
            print(f"      Fixed circular: {team_name} ({stat_count} stats)")
        
        if fixes_applied > 0:
            db.conn.commit()
    
    return fixes_applied

def fix_complex_mappings(league_name: str) -> int:
    """Fix teams pointing to other teams that don't have stats."""
    fixes_applied = 0
    
    with get_database() as db:
        # Find teams pointing to teams without stats
        complex_mappings = db.execute_query('''
            SELECT t1.team_id, t1.team_name, t1.canonical_team_id, t2.team_name as canonical_name,
                   COUNT(ts1.team_id) as source_stats, COUNT(ts2.team_id) as target_stats
            FROM teams t1
            LEFT JOIN teams t2 ON t1.canonical_team_id = t2.team_id
            LEFT JOIN team_stats ts1 ON t1.team_id = ts1.team_id
            LEFT JOIN team_stats ts2 ON t2.team_id = ts2.team_id
            JOIN leagues l ON t1.league_id = l.league_id
            WHERE l.league_name = ? AND t1.team_id != t1.canonical_team_id
            GROUP BY t1.team_id, t1.team_name, t1.canonical_team_id, t2.team_name
            HAVING source_stats = 0 AND target_stats = 0
        ''', (league_name,))
        
        # Get CSV teams for this league
        csv_teams = get_csv_teams(league_name)
        
        for _, mapping in complex_mappings.iterrows():
            source_team = mapping['team_name']
            source_id = mapping['team_id']
            
            # Try to find a better target team
            better_target = find_better_target(source_team, csv_teams, league_name, db)
            
            if better_target:
                target_id = db.execute_scalar('''
                    SELECT t.team_id FROM teams t
                    JOIN leagues l ON t.league_id = l.league_id
                    WHERE t.team_name = ? AND l.league_name = ?
                ''', (better_target, league_name))
                
                if target_id:
                    db.conn.execute(
                        'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                        (target_id, source_id)
                    )
                    fixes_applied += 1
                    print(f"      Fixed mapping: {source_team} → {better_target}")
        
        if fixes_applied > 0:
            db.conn.commit()
    
    return fixes_applied

def get_csv_teams(league_name: str) -> List[str]:
    """Get team names from the CSV file."""
    csv_path = f"data/raw/{league_name}/{league_name}_team_stats.csv"
    csv_teams = []
    
    if os.path.exists(csv_path):
        try:
            with open(csv_path, 'r') as f:
                lines = f.readlines()
                for line in lines[1:]:  # Skip header
                    if ',' in line:
                        team_name = line.split(',')[0].strip().strip('"')
                        csv_teams.append(team_name)
        except Exception:
            pass
    
    return csv_teams

def find_better_target(source_team: str, csv_teams: List[str], league_name: str, db) -> str:
    """Find a better target team that has stats."""
    # Remove common prefixes/suffixes from source team
    base_name = source_team
    for prefix in ['FC ', 'KF ', 'AF ', 'KS ', 'CF ', 'SC ', 'AC ', 'AS ', 'CD ', 'CS ']:
        if base_name.startswith(prefix):
            base_name = base_name[len(prefix):]
            break
    
    for suffix in [' FC', ' KF', ' AF', ' KS', ' CF', ' SC', ' AC', ' AS', ' CD', ' CS']:
        if base_name.endswith(suffix):
            base_name = base_name[:-len(suffix)]
            break
    
    # Look for matches in CSV teams
    for csv_team in csv_teams:
        if (base_name.lower() in csv_team.lower() or 
            csv_team.lower() in base_name.lower() or
            base_name.lower() == csv_team.lower()):
            
            # Verify this team has stats
            stats = db.get_team_stats(csv_team, league_name)
            if not stats.empty:
                return csv_team
    
    return None

def fix_single_edge_case_league(league_name: str) -> Dict:
    """Fix a single edge case league."""
    print(f"🔧 Fixing: {league_name}")
    
    # Get initial state
    with get_database() as db:
        initial_stats = db.execute_query('''
            SELECT COUNT(DISTINCT t.team_id) as working_teams,
                   COUNT(t.team_id) as total_teams
            FROM teams t
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            GROUP BY l.league_name
        ''', (league_name,))
        
        if initial_stats.empty:
            return {'league_name': league_name, 'success': False, 'error': 'No teams found'}
        
        initial_working = len(db.execute_query('''
            SELECT DISTINCT t.team_id
            FROM teams t
            JOIN team_stats ts ON t.team_id = ts.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
        ''', (league_name,)))
        
        initial_total = initial_stats.iloc[0]['total_teams']
    
    # Apply fixes
    circular_fixes = fix_circular_references(league_name)
    complex_fixes = fix_complex_mappings(league_name)
    
    # Get final state
    with get_database() as db:
        final_working = len(db.execute_query('''
            SELECT DISTINCT t.team_id
            FROM teams t
            JOIN team_stats ts ON t.team_id = ts.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
        ''', (league_name,)))
    
    improvement = final_working - initial_working
    total_fixes = circular_fixes + complex_fixes
    
    initial_rate = (initial_working / initial_total) * 100 if initial_total > 0 else 0
    final_rate = (final_working / initial_total) * 100 if initial_total > 0 else 0
    
    print(f"   📊 {initial_working}/{initial_total} → {final_working}/{initial_total} ({improvement:+d} teams)")
    print(f"   🔧 Applied {total_fixes} fixes ({circular_fixes} circular + {complex_fixes} complex)")
    
    return {
        'league_name': league_name,
        'success': True,
        'initial_working': initial_working,
        'final_working': final_working,
        'total_teams': initial_total,
        'improvement': improvement,
        'total_fixes': total_fixes,
        'circular_fixes': circular_fixes,
        'complex_fixes': complex_fixes,
        'initial_rate': initial_rate,
        'final_rate': final_rate
    }

def fix_all_edge_cases():
    """Fix all edge case leagues."""
    print(f"🚨 FIXING {len(EDGE_CASE_LEAGUES)} EDGE CASE LEAGUES")
    print("=" * 70)
    
    results = []
    total_improvement = 0
    total_fixes = 0
    
    for i, league_name in enumerate(EDGE_CASE_LEAGUES, 1):
        print(f"\n[{i:2d}/{len(EDGE_CASE_LEAGUES)}] {league_name}")
        
        try:
            result = fix_single_edge_case_league(league_name)
            results.append(result)
            
            if result.get('success', False):
                total_improvement += result.get('improvement', 0)
                total_fixes += result.get('total_fixes', 0)
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append({
                'league_name': league_name,
                'success': False,
                'error': str(e)
            })
    
    return results, total_improvement, total_fixes

def print_edge_case_summary(results: List[Dict], total_improvement: int, total_fixes: int):
    """Print summary of edge case fixes."""
    print(f"\n🎉 EDGE CASE FIXES COMPLETE!")
    print("=" * 60)
    
    successful_results = [r for r in results if r.get('success', False)]
    improved_results = [r for r in successful_results if r.get('improvement', 0) > 0]
    
    print(f"📊 SUMMARY:")
    print(f"   Leagues processed: {len(results)}")
    print(f"   Successful: {len(successful_results)}")
    print(f"   Improved: {len(improved_results)}")
    print(f"   Total teams improved: {total_improvement}")
    print(f"   Total fixes applied: {total_fixes}")
    
    if improved_results:
        print(f"\n🏆 TOP IMPROVEMENTS:")
        top_improvements = sorted(improved_results, key=lambda x: x.get('improvement', 0), reverse=True)[:10]
        
        for result in top_improvements:
            improvement = result.get('improvement', 0)
            fixes = result.get('total_fixes', 0)
            final_rate = result.get('final_rate', 0)
            print(f"   {result['league_name']}: +{improvement} teams ({fixes} fixes, {final_rate:.1f}%)")
    
    # Show leagues that still need attention
    no_improvement = [r for r in successful_results if r.get('improvement', 0) == 0 and r.get('total_fixes', 0) > 0]
    if no_improvement:
        print(f"\n⚠️  STILL NEED ATTENTION ({len(no_improvement)} leagues):")
        for result in no_improvement[:5]:
            fixes = result.get('total_fixes', 0)
            rate = result.get('final_rate', 0)
            print(f"   {result['league_name']}: {fixes} fixes applied, {rate:.1f}% success rate")

def main():
    """Main function."""
    print(f"🚨 EDGE CASE LEAGUE FIXING SYSTEM")
    print("=" * 60)
    print("Targeting the 40 leagues that applied fixes but showed no improvement")
    print("Focus: Circular references and complex mappings")
    
    # Fix all edge cases
    results, total_improvement, total_fixes = fix_all_edge_cases()
    
    # Print summary
    print_edge_case_summary(results, total_improvement, total_fixes)
    
    print(f"\n💡 These fixes should significantly improve the edge case leagues!")
    print("Re-run the comprehensive analysis to see the overall improvement.")

if __name__ == "__main__":
    main()
