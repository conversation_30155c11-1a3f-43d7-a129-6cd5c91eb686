#!/usr/bin/env python3
"""
Enhanced ML Pipeline Test with Error Fixes

This version includes fixes for:
- NaN value handling
- SMOTE failures
- Over/Under prediction errors
- Model analysis issues
"""

import sys
import os
import pandas as pd
import numpy as np
from sklearn.impute import SimpleImputer

# Add src to path
sys.path.append('src')

def preprocess_data_with_imputation(X):
    """Preprocess data with proper imputation."""
    print("🔧 Applying data imputation...")
    
    # Create imputer for numeric data
    imputer = SimpleImputer(strategy='median')
    
    # Get numeric columns
    numeric_cols = X.select_dtypes(include=[np.number]).columns
    
    # Apply imputation
    X_imputed = X.copy()
    if len(numeric_cols) > 0:
        X_imputed[numeric_cols] = imputer.fit_transform(X_imputed[numeric_cols])
    
    # Check for remaining NaN values
    nan_count = X_imputed.isna().sum().sum()
    print(f"   Remaining NaN values: {nan_count}")
    
    return X_imputed

def safe_model_prediction(model, X, prediction_type):
    """Make predictions with proper error handling."""
    try:
        if hasattr(model, 'predict_proba'):
            probabilities = model.predict_proba(X)
            prediction = model.predict(X)
            return prediction, probabilities
        else:
            prediction = model.predict(X)
            return prediction, None
    except Exception as e:
        print(f"   ⚠️  Prediction error for {prediction_type}: {e}")
        return None, None

# This would be the enhanced version of the test script
