#!/usr/bin/env python3
"""
Fix Denmark 2nd Division
Currently at 48.3% (14/29) - clear duplicate patterns
"""

import sys
sys.path.append('src')
from database.football_db import get_database

LEAGUE_NAME = "DENMARK_2ND_DIVISION"

# Clear mappings based on analysis
SPECIFIC_MAPPINGS = [
    ("AB", "AB Copenhagen", "Use full name"),
    ("Ab Copenhagen", "AB Copenhagen", "Fix capitalization"),
    ("Akademisk Boldklub", "AB Copenhagen", "Map to AB Copenhagen"),
    ("BK Frem 1886", "BK Frem", "Remove year suffix"),
    ("Bk Frem", "BK Frem", "Fix capitalization"),
    ("Frem", "BK Frem", "Add BK prefix"),
    ("FC Helsingør", "Helsingør", "Remove FC prefix"),
    ("HIK", "Hellerup IK", "Use full name"),
    ("Hellerup Ik", "Hellerup IK", "Fix capitalization"),
    ("<PERSON>høj", "Ishøj IF", "Add IF suffix"),
    ("Kolding If", "Kolding IF", "Fix capitalization"),
    ("Næstved", "Næstved BK", "Add BK suffix"),
    ("Nykøbing", "Nykøbing FC", "Add FC suffix"),
    ("Skovshoved", "Skovshoved IF", "Add IF suffix"),
    ("Thisted", "Thisted FC", "Add FC suffix"),
]

def apply_mapping(old_team: str, target_team: str, reason: str) -> bool:
    """Apply a canonical mapping between two teams."""
    with get_database() as db:
        try:
            # Get team IDs using execute_scalar
            old_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (old_team, LEAGUE_NAME))
            
            target_id = db.execute_scalar('''
                SELECT t.team_id FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (target_team, LEAGUE_NAME))
            
            if not old_id:
                print(f"   ❌ Old team '{old_team}' not found")
                return False
            
            if not target_id:
                print(f"   ❌ Target team '{target_team}' not found")
                return False
            
            # Verify target has stats
            target_stats = db.get_team_stats(target_team, LEAGUE_NAME)
            if target_stats.empty:
                print(f"   ⚠️  Target team '{target_team}' has no stats - skipping")
                return False
            
            # Apply mapping using direct connection
            db.conn.execute(
                'UPDATE teams SET canonical_team_id = ? WHERE team_id = ?',
                (target_id, old_id)
            )
            print(f"   ✅ Applied mapping")
            return True
            
        except Exception as e:
            print(f"   ❌ Error applying mapping: {e}")
            return False

def main():
    print(f"🎯 FIXING {LEAGUE_NAME}")
    print("=" * 50)
    
    # Get initial status
    with get_database() as db:
        teams = db.execute_query('''
            SELECT t.team_name FROM teams t
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY t.team_name
        ''', (LEAGUE_NAME,))
        
        working_before = 0
        broken_teams = []
        working_teams = []
        
        for _, team in teams.iterrows():
            stats = db.get_team_stats(team['team_name'], LEAGUE_NAME)
            if not stats.empty:
                working_before += 1
                working_teams.append(team['team_name'])
            else:
                broken_teams.append(team['team_name'])
        
        before_rate = (working_before / len(teams)) * 100
        print(f"📊 Before: {working_before}/{len(teams)} ({before_rate:.1f}%)")
        print(f"❌ Broken teams: {broken_teams}")
        print(f"✅ Working teams: {working_teams}")
    
    # Apply mappings
    print(f"\n🔧 Applying {len(SPECIFIC_MAPPINGS)} mappings...")
    fixes_applied = 0
    
    for old_team, target_team, reason in SPECIFIC_MAPPINGS:
        print(f"\n🔧 Fixing: {old_team} → {target_team}")
        print(f"   Reason: {reason}")
        
        if apply_mapping(old_team, target_team, reason):
            fixes_applied += 1
    
    print(f"\n💾 Applied {fixes_applied} fixes")
    
    # Get final status
    with get_database() as db:
        working_after = 0
        for _, team in teams.iterrows():
            stats = db.get_team_stats(team['team_name'], LEAGUE_NAME)
            if not stats.empty:
                working_after += 1
        
        after_rate = (working_after / len(teams)) * 100
        print(f"\n📊 After: {working_after}/{len(teams)} ({after_rate:.1f}%)")
        print(f"📈 Improvement: {after_rate - before_rate:.1f}%")
        
        if after_rate >= 90:
            print(f"🎉 {LEAGUE_NAME} now above 90%!")
        elif after_rate >= 80:
            print(f"🎯 {LEAGUE_NAME} now above 80%!")
        elif after_rate >= 60:
            print(f"📈 {LEAGUE_NAME} now above 60%!")

if __name__ == "__main__":
    main()